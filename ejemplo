import json
import os
import time
import boto3
import decimal
from botocore.exceptions import ClientError

# Leer variables de entorno
REGION = os.environ["REGION"]
CLIENT_ID = os.environ["CLIENT_ID"]
USER_POOL_ID = os.environ["USER_POOL_ID"]
TABLE_NAME = os.environ["TABLE_NAME"]

# Inicializar clientes
cognito = boto3.client("cognito-idp", region_name=REGION)
dynamodb = boto3.resource("dynamodb", region_name=REGION)
table = dynamodb.Table(TABLE_NAME)

# Clase para manejar Decimals en JSON
class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o) if o % 1 != 0 else int(o)
        return super(DecimalEncoder, self).default(o)

def handler(event, context):
    print("Event received:", json.dumps(event, indent=2, cls=DecimalEncoder))

    http_method = event.get("httpMethod")
    path = event.get("path", "")
    headers = {k.lower(): v for k, v in event.get("headers", {}).items()}
    
    # 1. Parseo ROBUSTO del body (maneja casos: None, string vacío, JSON inválido)
    try:
        body_str = event.get("body", "{}")  # Default "{}" si body es None
        body = json.loads(body_str) if isinstance(body_str, str) else body_str
    except json.JSONDecodeError:
        return response(400, {"message": "Invalid JSON format in request body"})

    # 2. Enrutamiento de endpoints
    try:
        if http_method == "POST" and "/login" in path:
            return handle_login(body)
        elif http_method == "POST" and "/register" in path:
            return handle_register(body)
        elif http_method == "POST" and "/refresh" in path:
            return handle_refresh(body)
        elif http_method == "GET" and "/user" in path:
            return handle_get_user(event.get("queryStringParameters", {}))
        else:
            return response(404, {"message": "Endpoint not found"})
    except Exception as e:
        print("Unhandled error:", str(e))
        return response(500, {"message": "Internal server error"})

# --------------------------
# FUNCIONES PRINCIPALES  
# --------------------------

def handle_login(body):
    # Validación EXPLÍCITA de campos
    if not isinstance(body, dict) or not all(k in body for k in ["email", "password"]):
        return response(400, {"message": "Email and password are required"})
    
    email = body["email"]
    password = body["password"]

    try:
        # Autenticar con Cognito
        auth_response = cognito.initiate_auth(
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={
                "USERNAME": email,  # Cognito usa USERNAME (aunque sea email)
                "PASSWORD": password
            },
            ClientId=CLIENT_ID
        )
        
        # Obtener datos del usuario desde DynamoDB
        user_data = get_user_from_dynamo(email)
        
        return response(200, {
            "message": "Login successful",
            "tokens": {
                "idToken": auth_response["AuthenticationResult"]["IdToken"],
                "accessToken": auth_response["AuthenticationResult"]["AccessToken"],
                "refreshToken": auth_response["AuthenticationResult"]["RefreshToken"]
            },
            "user": user_data
        })

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        if error_code == "NotAuthorizedException":
            return response(401, {"message": "Invalid email or password"})
        elif error_code == "UserNotFoundException":
            return response(404, {"message": "User not found"})
        else:
            return response(500, {"message": f"Auth error: {str(e)}"})

def handle_register(body):
    # Validación de campos
    required_fields = ["email", "password", "name"]
    if not isinstance(body, dict) or not all(k in body for k in required_fields):
        return response(400, {"message": f"Required fields: {', '.join(required_fields)}"})

    email = body["email"]
    password = body["password"]
    name = body["name"]

    try:
        # Crear usuario en Cognito (auto-confirmado)
        cognito.admin_create_user(
            UserPoolId=USER_POOL_ID,
            Username=email,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "email_verified", "Value": "true"},
                {"Name": "name", "Value": name}
            ],
            TemporaryPassword=password,
            MessageAction="SUPPRESS"
        )

        # Hacer la contraseña permanente
        cognito.admin_set_user_password(
            UserPoolId=USER_POOL_ID,
            Username=email,
            Password=password,
            Permanent=True
        )

        # Guardar en DynamoDB
        user_item = {
            "PK": f"USER#{email}",
            "SK": "PROFILE",
            "email": email,
            "name": name,
            "status": "CONFIRMED",
            "created_at": int(time.time()),
            "auth_source": "COGNITO"
        }
        table.put_item(Item=user_item)

        return response(201, {
            "message": "User registered successfully",
            "user": {"email": email, "name": name}
        })

    except ClientError as e:
        if e.response["Error"]["Code"] == "UsernameExistsException":
            return response(409, {"message": "User already exists"})
        return response(500, {"message": f"Registration error: {str(e)}"})

def handle_refresh(body):
    if not isinstance(body, dict) or "refreshToken" not in body:
        return response(400, {"message": "refreshToken is required"})
    
    try:
        auth_response = cognito.initiate_auth(
            AuthFlow="REFRESH_TOKEN_AUTH",
            AuthParameters={
                "REFRESH_TOKEN": body["refreshToken"]
            },
            ClientId=CLIENT_ID
        )
        
        return response(200, {
            "idToken": auth_response["AuthenticationResult"]["IdToken"],
            "accessToken": auth_response["AuthenticationResult"]["AccessToken"]
        })
    except ClientError:
        return response(401, {"message": "Invalid refresh token"})

def handle_get_user(query_params):
    if not isinstance(query_params, dict) or "email" not in query_params:
        return response(400, {"message": "Email parameter is required"})
    
    user_data = get_user_from_dynamo(query_params["email"])
    if not user_data:
        return response(404, {"message": "User not found"})
    return response(200, user_data)

# --------------------------
# FUNCIONES AUXILIARES  
# --------------------------

def get_user_from_dynamo(email):
    try:
        response = table.get_item(
            Key={"PK": f"USER#{email}", "SK": "PROFILE"}
        )
        return convert_decimals_to_floats(response.get("Item", {}))
    except Exception as e:
        print("DynamoDB error:", str(e))
        return None

def convert_decimals_to_floats(obj):
    if isinstance(obj, list):
        return [convert_decimals_to_floats(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: convert_decimals_to_floats(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        return float(obj) if obj % 1 != 0 else int(obj)
    return obj

def response(status_code, body_dict):
    return {
        "statusCode": status_code,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET,POST,OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type,Authorization"
        },
        "body": json.dumps(body_dict, cls=DecimalEncoder)
    }