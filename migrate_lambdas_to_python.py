#!/usr/bin/env python3
"""
Script para migrar todas las funciones Lambda de Node.js a Python
Automatiza la actualización del runtime y código de cada función
"""

import json
import os
import subprocess
import zipfile
import tempfile
import shutil
from typing import Dict, List, Any
import boto3
from botocore.exceptions import ClientError

# Configuración
REGION = "us-east-2"
PYTHON_RUNTIME = "python3.9"
HANDLER = "index.handler"

# Mapeo de funciones a sus archivos Python correspondientes
FUNCTION_MAPPINGS = {
    "hopie-analytics-dev": "hopie-analytics-dev-python.py",
    "hopie-category-dev": "hopie-category-dev-python.py",
    "hopie-chat-dev": "hopie-chat-dev-python.py",
    "hopie-couple-dev": "hopie-couple-dev-python.py",
    "hopie-favorite-dev": "hopie-favorite-dev-python.py",
    "hopie-image-dev": "hopie-image-dev-python.py",
    "hopie-ingredient-dev": "hopie-ingredient-dev-python.py",
    "hopie-lifeplan-dev": "hopie-lifeplan-dev-python.py",
    "hopie-location-dev": "hopie-location-dev-python.py",
    "hopie-meal-plan-dev": "hopie-meal-plan-dev-python.py",
    "hopie-message-history-dev": "hopie-message-history-dev-python.py",
    "hopie-notification-dev": "hopie-notification-dev-python.py",
    "hopie-places-dev": "hopie-places-dev-python.py",
    "hopie-questions-dev": "hopie-questions-dev-python.py",
    "hopie-recipe-dev": "hopie-recipe-dev-python.py",
    "hopie-report-dev": "hopie-report-dev-python.py",
    "hopie-review-dev": "hopie-review-dev-python.py",
    "hopie-scheduler-dev": "hopie-scheduler-dev-python.py",
    "hopie-search-dev": "hopie-search-dev-python.py",
    "hopie-shopping-list-dev": "hopie-shopping-list-dev-python.py",
    "hopie-stats-dev": "hopie-stats-dev-python.py",
    "hopie-tree-dev": "hopie-tree-dev-python.py",
    "hopie-user-dev": "hopie-user-dev-python.py",
    "hopie-websocket-connect-dev": "hopie-websocket-connect-dev-python.py",
    "hopie-websocket-disconnect-dev": "hopie-websocket-disconnect-dev-python.py"
}

# Plantilla genérica para funciones que no tienen conversión específica
GENERIC_TEMPLATE = "lambda_python_template.py"

def load_functions_data() -> List[Dict[str, Any]]:
    """
    Carga los datos de las funciones desde el archivo JSON
    """
    try:
        with open("all_nodejs_functions_details.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: No se encontró el archivo all_nodejs_functions_details.json")
        return []
    except json.JSONDecodeError:
        print("Error: El archivo JSON no es válido")
        return []

def create_deployment_package(function_name: str, python_file: str) -> str:
    """
    Crea un paquete de despliegue ZIP para la función Lambda
    """
    # Crear directorio temporal
    with tempfile.TemporaryDirectory() as temp_dir:
        # Copiar el archivo Python como index.py
        source_file = python_file if os.path.exists(python_file) else GENERIC_TEMPLATE
        target_file = os.path.join(temp_dir, "index.py")
        shutil.copy2(source_file, target_file)

        # Copiar requirements.txt si existe
        if os.path.exists("requirements.txt"):
            shutil.copy2("requirements.txt", os.path.join(temp_dir, "requirements.txt"))

        # Crear el archivo ZIP
        zip_filename = f"{function_name}-python.zip"
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(target_file, "index.py")
            if os.path.exists("requirements.txt"):
                zipf.write("requirements.txt", "requirements.txt")

        return zip_filename

def update_lambda_function(function_data: Dict[str, Any]) -> bool:
    """
    Actualiza una función Lambda específica
    """
    function_name = function_data["FunctionName"]
    function_arn = function_data["FunctionArn"]
    
    print(f"\n🔄 Migrando función: {function_name}")
    
    try:
        # Determinar qué archivo Python usar
        python_file = FUNCTION_MAPPINGS.get(function_name, GENERIC_TEMPLATE)
        
        # Crear paquete de despliegue
        print(f"   📦 Creando paquete de despliegue usando: {python_file}")
        zip_file = create_deployment_package(function_name, python_file)
        
        # Inicializar cliente Lambda
        lambda_client = boto3.client('lambda', region_name=REGION)
        
        # 1. Actualizar el código de la función
        print(f"   📤 Actualizando código de la función...")
        with open(zip_file, 'rb') as f:
            zip_content = f.read()
        
        lambda_client.update_function_code(
            FunctionName=function_name,
            ZipFile=zip_content
        )
        
        # 2. Actualizar la configuración (runtime y handler)
        print(f"   ⚙️  Actualizando runtime a {PYTHON_RUNTIME}...")
        lambda_client.update_function_configuration(
            FunctionName=function_name,
            Runtime=PYTHON_RUNTIME,
            Handler=HANDLER
        )
        
        # Limpiar archivo temporal
        os.remove(zip_file)
        
        print(f"   ✅ Función {function_name} migrada exitosamente")
        return True
        
    except ClientError as e:
        print(f"   ❌ Error AWS al migrar {function_name}: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Error general al migrar {function_name}: {e}")
        return False

def verify_all_python_files_exist(functions_data: List[Dict[str, Any]]) -> bool:
    """
    Verifica que todos los archivos Python existan
    """
    print("\n📝 Verificando archivos Python...")

    missing_files = []
    for function_data in functions_data:
        function_name = function_data["FunctionName"]
        python_file = FUNCTION_MAPPINGS.get(function_name)

        if not python_file or not os.path.exists(python_file):
            missing_files.append(function_name)

    if missing_files:
        print(f"❌ Faltan archivos Python para: {', '.join(missing_files)}")
        print("   Ejecuta primero: python generar_todas_las_funciones.py")
        return False

    print(f"✅ Todos los archivos Python están disponibles ({len(functions_data)} funciones)")
    return True

def main():
    """
    Función principal del script de migración
    """
    print("🚀 Iniciando migración de funciones Lambda de Node.js a Python")
    print("=" * 60)
    
    # Cargar datos de las funciones
    functions_data = load_functions_data()
    if not functions_data:
        print("❌ No se pudieron cargar los datos de las funciones")
        return
    
    print(f"📊 Se encontraron {len(functions_data)} funciones para migrar")
    
    # Verificar que todos los archivos Python existan
    if not verify_all_python_files_exist(functions_data):
        return
    
    # Confirmar antes de proceder
    print(f"\n⚠️  ATENCIÓN: Se van a migrar {len(functions_data)} funciones Lambda")
    print("   Esto cambiará el runtime de nodejs18.x a python3.9")
    print("   y reemplazará el código de cada función")
    
    confirm = input("\n¿Deseas continuar? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ Migración cancelada por el usuario")
        return
    
    # Migrar cada función
    successful_migrations = 0
    failed_migrations = 0
    
    for function_data in functions_data:
        success = update_lambda_function(function_data)
        if success:
            successful_migrations += 1
        else:
            failed_migrations += 1
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE MIGRACIÓN:")
    print(f"   ✅ Funciones migradas exitosamente: {successful_migrations}")
    print(f"   ❌ Funciones con errores: {failed_migrations}")
    print(f"   📈 Total procesadas: {len(functions_data)}")
    
    if failed_migrations == 0:
        print("\n🎉 ¡Migración completada exitosamente!")
    else:
        print(f"\n⚠️  Migración completada con {failed_migrations} errores")
        print("   Revisa los logs anteriores para más detalles")

if __name__ == "__main__":
    main()
