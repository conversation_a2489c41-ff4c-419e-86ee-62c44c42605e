const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/trees')) {
            return await getTrees(event);
        } else if (httpMethod === 'POST' && path.includes('/trees')) {
            return await createTree(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/tree/')) {
            return await getTreeById(event);
        } else if (httpMethod === 'PUT' && path.includes('/tree/')) {
            return await updateTree(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/tree/')) {
            return await deleteTree(event);
        } else if (httpMethod === 'POST' && path.includes('/tree/water')) {
            return await waterTree(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/tree/growth')) {
            return await getTreeGrowth(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getTrees(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'TREE#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            trees: result.Items || []
        })
    };
}

async function createTree(body, event) {
    const userId = 'user-123';
    const treeId = `tree-${Date.now()}`;
    const { name, type, location, plantedDate } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`,
            GSI1PK: 'TREE',
            GSI1SK: treeId,
            id: treeId,
            userId,
            name,
            type,
            location,
            plantedDate,
            growthLevel: 1,
            waterLevel: 100,
            health: 100,
            lastWatered: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree created successfully',
            tree: params.Item
        })
    };
}

async function getTreeById(event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || 'tree-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            tree: result.Item || null
        })
    };
}

async function updateTree(body, event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || 'tree-123';
    const { name, type, location } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        },
        UpdateExpression: 'SET #name = :name, #type = :type, #location = :location, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name',
            '#type': 'type',
            '#location': 'location'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':type': type,
            ':location': location,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree updated successfully',
            tree: result.Attributes
        })
    };
}

async function deleteTree(event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || 'tree-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree deleted successfully'
        })
    };
}

async function waterTree(body, event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || body.treeId || 'tree-123';
    const waterAmount = body.waterAmount || 20;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        },
        UpdateExpression: 'SET waterLevel = waterLevel + :waterAmount, lastWatered = :lastWatered, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':waterAmount': waterAmount,
            ':lastWatered': new Date().toISOString(),
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Tree watered successfully',
            tree: result.Attributes
        })
    };
}

async function getTreeGrowth(event) {
    const userId = 'user-123';
    const treeId = event.pathParameters?.id || event.queryStringParameters?.treeId || 'tree-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `TREE#${treeId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    if (result.Item) {
        const tree = result.Item;
        const daysSincePlanted = Math.floor((new Date() - new Date(tree.plantedDate)) / (1000 * 60 * 60 * 24));
        const growthProgress = Math.min(100, (daysSincePlanted / 30) * 100);

        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                treeId,
                daysSincePlanted,
                growthProgress,
                currentLevel: tree.growthLevel,
                waterLevel: tree.waterLevel,
                health: tree.health
            })
        };
    }

    return {
        statusCode: 404,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({ error: 'Tree not found' })
    };
}
