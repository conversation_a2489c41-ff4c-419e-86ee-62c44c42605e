import json
import os
import time
import boto3
import decimal
from botocore.exceptions import ClientError
from typing import Dict, Any, Optional, List

# Leer variables de entorno
TABLE_NAME = os.environ.get("TABLE_NAME")

# Inicializar clientes AWS
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(TABLE_NAME) if TABLE_NAME else None

# Clase para manejar Decimals en JSON
class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o) if o % 1 != 0 else int(o)
        return super(DecimalEncoder, self).default(o)

def handler(event, context):
    """
    Handler principal para hopie-user-dev
    """
    print("Event received:", json.dumps(event, indent=2, cls=DecimalEncoder))

    http_method = event.get("httpMethod")
    path = event.get("path", "")
    headers = {k.lower(): v for k, v in event.get("headers", {}).items()}
    query_params = event.get("queryStringParameters") or {}
    path_params = event.get("pathParameters") or {}
    
    # Parseo robusto del body
    try:
        body_str = event.get("body", "{}")
        body = json.loads(body_str) if isinstance(body_str, str) and body_str else {}
    except json.JSONDecodeError:
        return response(400, {"message": "Invalid JSON format in request body"})

    # Enrutamiento de endpoints
    try:
        if http_method == "GET" and "/profile" in path:
            return handle_get_user_profile(query_params, path_params)
        elif http_method == "PUT" and "/profile" in path:
            return handle_update_user_profile(body, path_params)
        elif http_method == "GET" and "/preferences" in path:
            return handle_get_user_preferences(query_params, path_params)
        elif http_method == "PUT" and "/preferences" in path:
            return handle_update_user_preferences(body, path_params)
        else:
            return response(404, {"message": "Endpoint not found"})
        
    except Exception as e:
        print("Unhandled error:", str(e))
        return response(500, {"message": "Internal server error"})

# --------------------------
# FUNCIONES PRINCIPALES
# --------------------------

def handle_get_user_profile(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Obtiene el perfil del usuario
    """
    user_id = "user-123"  # En producción esto vendría del token de autenticación
    
    user_data = dynamo_get_item(f"USER#{user_id}", "PROFILE")
    
    if not user_data:
        # Datos por defecto si no existe el usuario
        user_data = {
            "id": user_id,
            "name": "Usuario Demo",
            "email": "<EMAIL>"
        }
    
    return response(200, {"user": user_data})

def handle_update_user_profile(body: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Actualiza el perfil del usuario
    """
    user_id = "user-123"  # En producción esto vendría del token de autenticación
    
    # Validar campos requeridos
    validation_error = validate_required_fields(body, ["name", "email"])
    if validation_error:
        return response(400, {"message": validation_error})
    
    name = body.get("name")
    email = body.get("email")
    phone = body.get("phone")
    birth_date = body.get("birthDate")
    
    # Actualizar en DynamoDB
    update_expression = "SET #name = :name, email = :email, phone = :phone, birthDate = :birthDate, updatedAt = :updatedAt"
    expression_values = {
        ":name": name,
        ":email": email,
        ":phone": phone,
        ":birthDate": birth_date,
        ":updatedAt": get_current_timestamp()
    }
    expression_names = {"#name": "name"}
    
    updated_user = dynamo_update_item(
        f"USER#{user_id}", 
        "PROFILE", 
        update_expression, 
        expression_values, 
        expression_names
    )
    
    if updated_user:
        return response(200, {
            "message": "Profile updated successfully",
            "user": updated_user
        })
    else:
        return response(500, {"message": "Failed to update profile"})

def handle_get_user_preferences(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Obtiene las preferencias del usuario
    """
    user_id = "user-123"  # En producción esto vendría del token de autenticación
    
    preferences_data = dynamo_get_item(f"USER#{user_id}", "PREFERENCES")
    
    if not preferences_data:
        # Preferencias por defecto
        preferences_data = {
            "notifications": True,
            "theme": "light",
            "language": "es"
        }
    
    return response(200, {"preferences": preferences_data})

def handle_update_user_preferences(body: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Actualiza las preferencias del usuario
    """
    user_id = "user-123"  # En producción esto vendría del token de autenticación
    
    # Actualizar en DynamoDB
    update_expression = "SET preferences = :preferences, updatedAt = :updatedAt"
    expression_values = {
        ":preferences": body,
        ":updatedAt": get_current_timestamp()
    }
    
    updated_preferences = dynamo_update_item(
        f"USER#{user_id}", 
        "PREFERENCES", 
        update_expression, 
        expression_values
    )
    
    if updated_preferences:
        return response(200, {
            "message": "Preferences updated successfully",
            "preferences": updated_preferences
        })
    else:
        return response(500, {"message": "Failed to update preferences"})

# --------------------------
# FUNCIONES AUXILIARES
# --------------------------

def response(status_code: int, body_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Genera una respuesta HTTP estándar con headers CORS
    """
    return {
        "statusCode": status_code,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type,Authorization"
        },
        "body": json.dumps(body_dict, cls=DecimalEncoder)
    }

def convert_decimals_to_floats(obj):
    """
    Convierte objetos Decimal de DynamoDB a tipos nativos de Python
    """
    if isinstance(obj, list):
        return [convert_decimals_to_floats(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: convert_decimals_to_floats(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        return float(obj) if obj % 1 != 0 else int(obj)
    return obj

def get_current_timestamp() -> str:
    """
    Obtiene timestamp actual en formato ISO
    """
    return time.strftime('%Y-%m-%dT%H:%M:%S.%fZ', time.gmtime())

def dynamo_get_item(pk: str, sk: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un item de DynamoDB
    """
    try:
        response = table.get_item(Key={"PK": pk, "SK": sk})
        item = response.get("Item")
        return convert_decimals_to_floats(item) if item else None
    except Exception as e:
        print(f"DynamoDB get error: {str(e)}")
        return None

def dynamo_update_item(pk: str, sk: str, update_expression: str, 
                      expression_values: Dict[str, Any], 
                      expression_names: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
    """
    Actualiza un item en DynamoDB
    """
    try:
        params = {
            "Key": {"PK": pk, "SK": sk},
            "UpdateExpression": update_expression,
            "ExpressionAttributeValues": expression_values,
            "ReturnValues": "ALL_NEW"
        }
        if expression_names:
            params["ExpressionAttributeNames"] = expression_names
            
        response = table.update_item(**params)
        return convert_decimals_to_floats(response.get("Attributes"))
    except Exception as e:
        print(f"DynamoDB update error: {str(e)}")
        return None

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
    """
    Valida que todos los campos requeridos estén presentes
    """
    if not isinstance(data, dict):
        return "Request body must be a JSON object"
    
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        return f"Missing required fields: {', '.join(missing_fields)}"
    
    return None
