const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const eventbridge = new AWS.EventBridge();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Scheduler Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/schedules')) {
            return await getSchedules(event);
        } else if (httpMethod === 'POST' && path.includes('/schedules')) {
            return await createSchedule(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/schedule/')) {
            return await getScheduleById(event);
        } else if (httpMethod === 'PUT' && path.includes('/schedule/')) {
            return await updateSchedule(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/schedule/')) {
            return await deleteSchedule(event);
        } else if (httpMethod === 'POST' && path.includes('/schedule/trigger')) {
            return await triggerSchedule(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/schedule/upcoming')) {
            return await getUpcomingSchedules(event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getSchedules(event) {
    const userId = 'user-123';
    const type = event.queryStringParameters?.type || 'all';
    
    let params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'SCHEDULE#'
        }
    };
    
    if (type !== 'all') {
        params.FilterExpression = '#type = :type';
        params.ExpressionAttributeNames = { '#type': 'type' };
        params.ExpressionAttributeValues[':type'] = type;
    }
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            schedules: result.Items || []
        })
    };
}

async function createSchedule(body, event) {
    const userId = 'user-123';
    const scheduleId = `schedule-${Date.now()}`;
    const { title, description, type, scheduledTime, recurrence, action, isActive } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`,
            GSI1PK: 'SCHEDULE',
            GSI1SK: scheduleId,
            id: scheduleId,
            userId,
            title,
            description,
            type: type || 'reminder',
            scheduledTime,
            recurrence: recurrence || 'none',
            action: action || {},
            isActive: isActive !== false,
            lastTriggered: null,
            nextTrigger: scheduledTime,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule created successfully',
            schedule: params.Item
        })
    };
}

async function getScheduleById(event) {
    const userId = 'user-123';
    const scheduleId = event.pathParameters?.id || 'schedule-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            schedule: result.Item || null
        })
    };
}

async function updateSchedule(body, event) {
    const userId = 'user-123';
    const scheduleId = event.pathParameters?.id || 'schedule-123';
    const { title, description, scheduledTime, recurrence, action, isActive } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        },
        UpdateExpression: 'SET title = :title, description = :description, scheduledTime = :scheduledTime, recurrence = :recurrence, action = :action, isActive = :isActive, nextTrigger = :nextTrigger, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':scheduledTime': scheduledTime,
            ':recurrence': recurrence,
            ':action': action,
            ':isActive': isActive,
            ':nextTrigger': scheduledTime,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule updated successfully',
            schedule: result.Attributes
        })
    };
}

async function deleteSchedule(event) {
    const userId = 'user-123';
    const scheduleId = event.pathParameters?.id || 'schedule-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule deleted successfully'
        })
    };
}

async function triggerSchedule(body, event) {
    const userId = 'user-123';
    const { scheduleId } = body;
    
    // Get schedule
    const getParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        }
    };
    
    const scheduleResult = await dynamodb.get(getParams).promise();
    const schedule = scheduleResult.Item;
    
    if (!schedule) {
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Schedule not found' })
        };
    }
    
    // Execute the scheduled action
    let actionResult = {};
    
    switch (schedule.action.type) {
        case 'notification':
            actionResult = await createNotification(schedule.action.data);
            break;
        case 'reminder':
            actionResult = await createReminder(schedule.action.data);
            break;
        case 'water_tree':
            actionResult = await waterTree(schedule.action.data);
            break;
        default:
            actionResult = { message: 'Unknown action type' };
    }
    
    // Update last triggered time
    const updateParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `SCHEDULE#${scheduleId}`
        },
        UpdateExpression: 'SET lastTriggered = :lastTriggered, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':lastTriggered': new Date().toISOString(),
            ':updatedAt': new Date().toISOString()
        }
    };
    
    await dynamodb.update(updateParams).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Schedule triggered successfully',
            actionResult
        })
    };
}

async function getUpcomingSchedules(event) {
    const userId = 'user-123';
    const hours = parseInt(event.queryStringParameters?.hours || '24');
    
    const now = new Date();
    const futureTime = new Date(now.getTime() + (hours * 60 * 60 * 1000));
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        FilterExpression: 'isActive = :isActive AND nextTrigger BETWEEN :now AND :future',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'SCHEDULE#',
            ':isActive': true,
            ':now': now.toISOString(),
            ':future': futureTime.toISOString()
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            upcomingSchedules: result.Items || [],
            timeframe: `${hours} hours`
        })
    };
}

async function createNotification(data) {
    const notificationId = `notification-${Date.now()}`;
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${data.userId}`,
            SK: `NOTIFICATION#${notificationId}`,
            id: notificationId,
            title: data.title,
            message: data.message,
            type: 'scheduled',
            read: false,
            createdAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    return { notificationId, message: 'Notification created' };
}

async function createReminder(data) {
    // Similar to notification but with reminder-specific logic
    return await createNotification(data);
}

async function waterTree(data) {
    const { treeId, waterAmount } = data;
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${data.userId}`,
            SK: `TREE#${treeId}`
        },
        UpdateExpression: 'SET waterLevel = waterLevel + :waterAmount, lastWatered = :lastWatered',
        ExpressionAttributeValues: {
            ':waterAmount': waterAmount || 20,
            ':lastWatered': new Date().toISOString()
        }
    };
    
    await dynamodb.update(params).promise();
    return { treeId, message: 'Tree watered automatically' };
}
