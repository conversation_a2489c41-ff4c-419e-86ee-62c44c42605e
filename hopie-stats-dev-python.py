import json
import os
import time
import boto3
import decimal
from botocore.exceptions import ClientError
from typing import Dict, Any, Optional, List

# Leer variables de entorno
TABLE_NAME = os.environ.get("TABLE_NAME")
BUCKET_NAME = os.environ.get("BUCKET_NAME")  # Solo para funciones que lo necesiten
WEBSOCKET_ENDPOINT = os.environ.get("WEBSOCKET_ENDPOINT")  # Solo para funciones que lo necesiten

# Inicializar clientes AWS
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(TABLE_NAME) if TABLE_NAME else None
s3 = boto3.client("s3")  # Solo para funciones que lo necesiten

# Clase para manejar Decimals en JSON
class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o) if o % 1 != 0 else int(o)
        return super(DecimalEncoder, self).default(o)

def handler(event, context):
    """
    Handler principal para hopie-stats-dev
    """
    print("Event received:", json.dumps(event, indent=2, cls=DecimalEncoder))

    http_method = event.get("httpMethod")
    path = event.get("path", "")
    headers = {k.lower(): v for k, v in event.get("headers", {}).items()}
    query_params = event.get("queryStringParameters") or {}
    path_params = event.get("pathParameters") or {}
    
    # Parseo robusto del body
    try:
        body_str = event.get("body", "{}")
        body = json.loads(body_str) if isinstance(body_str, str) and body_str else {}
    except json.JSONDecodeError:
        return response(400, {"message": "Invalid JSON format in request body"})

    # Enrutamiento de endpoints - ESTE BLOQUE DEBE SER PERSONALIZADO POR FUNCIÓN
    try:
        if http_method == "GET" and "/stats/user" in path:
            return handle_get_stats_user(query_params, path_params)
        if http_method == "GET" and "/stats/couple" in path:
            return handle_get_stats_couple(query_params, path_params)
        if http_method == "GET" and "/stats/activities" in path:
            return handle_get_stats_activities(query_params, path_params)
        if http_method == "GET" and "/stats/progress" in path:
            return handle_get_stats_progress(query_params, path_params)
        if http_method == "POST" and "/stats/track" in path:
            return handle_post_stats_track(body, path_params)
        if http_method == "GET" and "/stats/user" in path:
            return handle_get_stats_user(query_params, path_params)
        if http_method == "GET" and "/stats/couple" in path:
            return handle_get_stats_couple(query_params, path_params)
        if http_method == "GET" and "/stats/activities" in path:
            return handle_get_stats_activities(query_params, path_params)
        if http_method == "GET" and "/stats/progress" in path:
            return handle_get_stats_progress(query_params, path_params)
        if http_method == "POST" and "/stats/track" in path:
            return handle_post_stats_track(body, path_params)
        else:
            return response(404, {"message": "Endpoint not found"})
        
    except Exception as e:
        print("Unhandled error:", str(e))
        return response(500, {"message": "Internal server error"})

# --------------------------
# FUNCIONES AUXILIARES COMUNES
# --------------------------

def response(status_code: int, body_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Genera una respuesta HTTP estándar con headers CORS
    """
    return {
        "statusCode": status_code,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type,Authorization"
        },
        "body": json.dumps(body_dict, cls=DecimalEncoder)
    }

def convert_decimals_to_floats(obj):
    """
    Convierte objetos Decimal de DynamoDB a tipos nativos de Python
    """
    if isinstance(obj, list):
        return [convert_decimals_to_floats(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: convert_decimals_to_floats(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        return float(obj) if obj % 1 != 0 else int(obj)
    return obj

def get_current_timestamp() -> str:
    """
    Obtiene timestamp actual en formato ISO
    """
    return time.strftime('%Y-%m-%dT%H:%M:%S.%fZ', time.gmtime())

# --------------------------
# FUNCIONES DYNAMODB COMUNES
# --------------------------

def dynamo_get_item(pk: str, sk: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un item de DynamoDB
    """
    try:
        response = table.get_item(Key={"PK": pk, "SK": sk})
        item = response.get("Item")
        return convert_decimals_to_floats(item) if item else None
    except Exception as e:
        print(f"DynamoDB get error: {str(e)}")
        return None

def dynamo_put_item(item: Dict[str, Any]) -> bool:
    """
    Inserta un item en DynamoDB
    """
    try:
        table.put_item(Item=item)
        return True
    except Exception as e:
        print(f"DynamoDB put error: {str(e)}")
        return False

def dynamo_update_item(pk: str, sk: str, update_expression: str, 
                      expression_values: Dict[str, Any], 
                      expression_names: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
    """
    Actualiza un item en DynamoDB
    """
    try:
        params = {
            "Key": {"PK": pk, "SK": sk},
            "UpdateExpression": update_expression,
            "ExpressionAttributeValues": expression_values,
            "ReturnValues": "ALL_NEW"
        }
        if expression_names:
            params["ExpressionAttributeNames"] = expression_names
            
        response = table.update_item(**params)
        return convert_decimals_to_floats(response.get("Attributes"))
    except Exception as e:
        print(f"DynamoDB update error: {str(e)}")
        return None

def dynamo_delete_item(pk: str, sk: str) -> bool:
    """
    Elimina un item de DynamoDB
    """
    try:
        table.delete_item(Key={"PK": pk, "SK": sk})
        return True
    except Exception as e:
        print(f"DynamoDB delete error: {str(e)}")
        return False

def dynamo_query_gsi(gsi_name: str, gsi_pk: str, gsi_sk_prefix: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Realiza una query en un GSI
    """
    try:
        params = {
            "IndexName": gsi_name,
            "KeyConditionExpression": "GSI1PK = :gsi1pk",
            "ExpressionAttributeValues": {":gsi1pk": gsi_pk}
        }
        
        if gsi_sk_prefix:
            params["KeyConditionExpression"] += " AND begins_with(GSI1SK, :gsi1sk)"
            params["ExpressionAttributeValues"][":gsi1sk"] = gsi_sk_prefix
            
        response = table.query(**params)
        return convert_decimals_to_floats(response.get("Items", []))
    except Exception as e:
        print(f"DynamoDB query error: {str(e)}")
        return []

# --------------------------
# FUNCIONES S3 COMUNES (para funciones que lo necesiten)
# --------------------------

def s3_generate_presigned_url(bucket: str, key: str, expiration: int = 300) -> Optional[str]:
    """
    Genera una URL presignada para subir archivos a S3
    """
    try:
        url = s3.generate_presigned_url(
            'put_object',
            Params={'Bucket': bucket, 'Key': key},
            ExpiresIn=expiration
        )
        return url
    except Exception as e:
        print(f"S3 presigned URL error: {str(e)}")
        return None

def s3_delete_object(bucket: str, key: str) -> bool:
    """
    Elimina un objeto de S3
    """
    try:
        s3.delete_object(Bucket=bucket, Key=key)
        return True
    except Exception as e:
        print(f"S3 delete error: {str(e)}")
        return False

# --------------------------
# FUNCIONES DE UTILIDAD
# --------------------------

def generate_id(prefix: str) -> str:
    """
    Genera un ID único con prefijo
    """
    return f"{prefix}-{int(time.time() * 1000)}"

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
    """
    Valida que todos los campos requeridos estén presentes
    """
    if not isinstance(data, dict):
        return "Request body must be a JSON object"
    
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        return f"Missing required fields: {', '.join(missing_fields)}"
    
    return None


# --------------------------
# HANDLERS ESPECÍFICOS
# --------------------------

def handle_get_stats_user(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/user - User/Profile operation"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    
    if "GET" == "GET":
        user_data = dynamo_get_item(f"USER#{user_id}", "PROFILE")
        if not user_data:
            user_data = {"id": user_id, "name": "Usuario Demo", "email": "<EMAIL>"}
        return response(200, {"user": user_data})
    
    elif "GET" == "PUT":
        # Actualizar perfil de usuario
        update_expression = "SET #name = :name, email = :email, updatedAt = :updatedAt"
        expression_values = {
            ":name": data.get("name"),
            ":email": data.get("email"),
            ":updatedAt": get_current_timestamp()
        }
        expression_names = {"#name": "name"}
        
        updated_user = dynamo_update_item(f"USER#{user_id}", "PROFILE", update_expression, expression_values, expression_names)
        if updated_user:
            return response(200, {"message": "Profile updated successfully", "user": updated_user})
        else:
            return response(500, {"message": "Failed to update profile"})
    
    else:
        return response(405, {"message": "Method not allowed"})

def handle_get_stats_couple(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/couple - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint GET /stats/couple ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_get_stats_activities(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/activities - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint GET /stats/activities ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_get_stats_progress(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/progress - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint GET /stats/progress ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_post_stats_track(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para POST /stats/track - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint POST /stats/track ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_get_stats_user(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/user - User/Profile operation"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    
    if "GET" == "GET":
        user_data = dynamo_get_item(f"USER#{user_id}", "PROFILE")
        if not user_data:
            user_data = {"id": user_id, "name": "Usuario Demo", "email": "<EMAIL>"}
        return response(200, {"user": user_data})
    
    elif "GET" == "PUT":
        # Actualizar perfil de usuario
        update_expression = "SET #name = :name, email = :email, updatedAt = :updatedAt"
        expression_values = {
            ":name": data.get("name"),
            ":email": data.get("email"),
            ":updatedAt": get_current_timestamp()
        }
        expression_names = {"#name": "name"}
        
        updated_user = dynamo_update_item(f"USER#{user_id}", "PROFILE", update_expression, expression_values, expression_names)
        if updated_user:
            return response(200, {"message": "Profile updated successfully", "user": updated_user})
        else:
            return response(500, {"message": "Failed to update profile"})
    
    else:
        return response(405, {"message": "Method not allowed"})

def handle_get_stats_couple(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/couple - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint GET /stats/couple ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_get_stats_activities(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/activities - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint GET /stats/activities ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_get_stats_progress(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para GET /stats/progress - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint GET /stats/progress ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })

def handle_post_stats_track(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para POST /stats/track - hopie-stats-dev operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {
        "message": f"Endpoint POST /stats/track ejecutado correctamente",
        "function_type": "hopie-stats-dev",
        "data": data,
        "path_params": path_params
    })