const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/couples')) {
            return await getCouples(event);
        } else if (httpMethod === 'POST' && path.includes('/couples')) {
            return await createCouple(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/couple/')) {
            return await getCoupleById(event);
        } else if (httpMethod === 'PUT' && path.includes('/couple/')) {
            return await updateCouple(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/couple/')) {
            return await deleteCouple(event);
        } else if (httpMethod === 'POST' && path.includes('/couple/invite')) {
            return await invitePartner(requestBody, event);
        } else if (httpMethod === 'POST' && path.includes('/couple/accept')) {
            return await acceptInvitation(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getCouples(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'COUPLE#'
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            couples: result.Items || []
        })
    };
}

async function createCouple(body, event) {
    const userId = 'user-123';
    const coupleId = `couple-${Date.now()}`;
    const { partnerEmail, relationshipType, startDate } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`,
            GSI1PK: 'COUPLE',
            GSI1SK: coupleId,
            id: coupleId,
            userId,
            partnerEmail,
            relationshipType,
            startDate,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple relationship created successfully',
            couple: params.Item
        })
    };
}

async function getCoupleById(event) {
    const userId = 'user-123';
    const coupleId = event.pathParameters?.id || 'couple-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            couple: result.Item || null
        })
    };
}

async function updateCouple(body, event) {
    const userId = 'user-123';
    const coupleId = event.pathParameters?.id || 'couple-123';
    const { relationshipType, startDate, status } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`
        },
        UpdateExpression: 'SET relationshipType = :relationshipType, startDate = :startDate, #status = :status, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':relationshipType': relationshipType,
            ':startDate': startDate,
            ':status': status,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple relationship updated successfully',
            couple: result.Attributes
        })
    };
}

async function deleteCouple(event) {
    const userId = 'user-123';
    const coupleId = event.pathParameters?.id || 'couple-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `COUPLE#${coupleId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Couple relationship deleted successfully'
        })
    };
}

async function invitePartner(body, event) {
    const userId = 'user-123';
    const { partnerEmail, message } = body;
    
    // Logic to send invitation email would go here
    // For now, just return success
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Partner invitation sent successfully',
            invitedEmail: partnerEmail
        })
    };
}

async function acceptInvitation(body, event) {
    const userId = 'user-123';
    const { coupleId, accept } = body;
    
    if (accept) {
        const params = {
            TableName: TABLE_NAME,
            Key: {
                PK: `USER#${userId}`,
                SK: `COUPLE#${coupleId}`
            },
            UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'active',
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        };
        
        const result = await dynamodb.update(params).promise();
        
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                message: 'Invitation accepted successfully',
                couple: result.Attributes
            })
        };
    } else {
        // Delete the invitation
        const params = {
            TableName: TABLE_NAME,
            Key: {
                PK: `USER#${userId}`,
                SK: `COUPLE#${coupleId}`
            }
        };
        
        await dynamodb.delete(params).promise();
        
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                message: 'Invitation declined successfully'
            })
        };
    }
}
