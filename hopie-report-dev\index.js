const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/reports')) {
            return await createReport(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/reports')) {
            return await getReports(event);
        } else if (httpMethod === 'GET' && path.includes('/report/')) {
            return await getReportById(event);
        } else if (httpMethod === 'PUT' && path.includes('/report/')) {
            return await updateReportStatus(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function createReport(body, event) {
    const reportId = `report-${Date.now()}`;
    const userId = 'user-123';
    const { entityId, entityType, reason, description } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `REPORT#${reportId}`,
            SK: 'METADATA',
            GSI1PK: 'REPORT',
            GSI1SK: reportId,
            id: reportId,
            userId,
            entityId,
            entityType,
            reason,
            description,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Report created successfully',
            report: params.Item
        })
    };
}

async function getReports(event) {
    const status = event.queryStringParameters?.status || 'all';

    let params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'REPORT'
        }
    };

    if (status !== 'all') {
        params.FilterExpression = '#status = :status';
        params.ExpressionAttributeNames = { '#status': 'status' };
        params.ExpressionAttributeValues[':status'] = status;
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            reports: result.Items || []
        })
    };
}

async function getReportById(event) {
    const reportId = event.pathParameters?.id || 'report-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `REPORT#${reportId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            report: result.Item || null
        })
    };
}

async function updateReportStatus(body, event) {
    const reportId = event.pathParameters?.id || 'report-123';
    const { status, adminNotes } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `REPORT#${reportId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #status = :status, adminNotes = :adminNotes, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':status': status,
            ':adminNotes': adminNotes,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Report status updated successfully',
            report: result.Attributes
        })
    };
}
