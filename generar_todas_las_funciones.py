#!/usr/bin/env python3
"""
Script para generar automáticamente TODAS las 24 funciones Python
analizando cada archivo Node.js existente
"""

import json
import os
import re
from typing import Dict, List, Any, <PERSON><PERSON>

def load_functions_data() -> List[Dict[str, Any]]:
    """Carga los datos de las funciones desde el archivo JSON"""
    with open("all_nodejs_functions_details.json", "r") as f:
        return json.load(f)

def analyze_nodejs_file(function_name: str) -> Dict[str, Any]:
    """Analiza un archivo Node.js para extraer endpoints y funcionalidad"""
    nodejs_file = f"{function_name}/index.js"
    analysis = {
        "endpoints": [],
        "functions": [],
        "has_s3": False,
        "has_websocket": False,
        "is_websocket_handler": False,
        "environment_vars": ["TABLE_NAME"]
    }
    
    if not os.path.exists(nodejs_file):
        return analysis
    
    try:
        with open(nodejs_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Detectar si es un handler de WebSocket
        if "requestContext" in content and "connectionId" in content:
            analysis["is_websocket_handler"] = True
        
        # Detectar endpoints HTTP
        endpoint_patterns = [
            r"httpMethod === ['\"](\w+)['\"] && path\.includes\(['\"]([^'\"]+)['\"]",
            r"httpMethod === ['\"](\w+)['\"].*?['\"]([^'\"]*\/[^'\"]*)['\"]"
        ]
        
        for pattern in endpoint_patterns:
            matches = re.findall(pattern, content)
            for method, path in matches:
                analysis["endpoints"].append({"method": method, "path": path})
        
        # Detectar funciones definidas
        function_pattern = r"async function (\w+)\("
        functions = re.findall(function_pattern, content)
        analysis["functions"] = functions
        
        # Detectar uso de S3
        if "new AWS.S3()" in content or "s3." in content or "getSignedUrl" in content:
            analysis["has_s3"] = True
            analysis["environment_vars"].append("BUCKET_NAME")
        
        # Detectar WebSocket
        if "websocket" in content.lower() or "WEBSOCKET_ENDPOINT" in content:
            analysis["has_websocket"] = True
            analysis["environment_vars"].append("WEBSOCKET_ENDPOINT")
        
        return analysis
        
    except Exception as e:
        print(f"   ⚠️  Error analizando {nodejs_file}: {e}")
        return analysis

def generate_python_code(function_name: str, analysis: Dict[str, Any]) -> str:
    """Genera código Python completo basado en el análisis"""
    
    # Leer plantilla base
    with open("lambda_python_template.py", 'r', encoding='utf-8') as f:
        template = f.read()
    
    # Generar imports específicos
    imports = []
    if analysis["has_s3"]:
        imports.append("# S3 client ya incluido en plantilla base")
    if analysis["has_websocket"]:
        imports.append("# WebSocket support ya incluido en plantilla base")
    
    # Generar código de enrutamiento
    routing_code = []
    handler_functions = []
    
    if analysis["is_websocket_handler"]:
        # Handler especial para WebSocket
        if "connect" in function_name:
            routing_code.append('        # WebSocket Connect Handler')
            routing_code.append('        return handle_websocket_connect(event, context)')
            handler_functions.append(generate_websocket_connect_handler())
        elif "disconnect" in function_name:
            routing_code.append('        # WebSocket Disconnect Handler')
            routing_code.append('        return handle_websocket_disconnect(event, context)')
            handler_functions.append(generate_websocket_disconnect_handler())
    else:
        # Handlers HTTP normales
        for endpoint in analysis["endpoints"]:
            method = endpoint["method"]
            path = endpoint["path"]
            
            # Crear nombre de función limpio
            clean_path = path.replace('/', '_').replace('-', '_').strip('_')
            func_name = f"handle_{method.lower()}_{clean_path}"
            
            # Agregar al enrutamiento
            routing_code.append(f'        if http_method == "{method}" and "{path}" in path:')
            if method in ["POST", "PUT"]:
                routing_code.append(f'            return {func_name}(body, path_params)')
            else:
                routing_code.append(f'            return {func_name}(query_params, path_params)')
            
            # Crear función handler
            handler_functions.append(generate_http_handler(func_name, method, path, function_name))
    
    # Si no se detectaron endpoints, crear handler genérico
    if not routing_code:
        routing_code.append('        # Handler genérico - implementar según necesidades específicas')
        routing_code.append('        return response(200, {"message": f"Function {function_name} executed successfully", "event": event})')
    
    # Reemplazar placeholder con código generado
    routing_section = "\n".join(routing_code)
    
    # Encontrar y reemplazar la sección de enrutamiento
    placeholder_start = template.find('        # PLACEHOLDER: Aquí se debe implementar la lógica específica de cada función')
    placeholder_end = template.find('        return response(404, {"message": "Endpoint not found"})')
    
    if placeholder_start != -1 and placeholder_end != -1:
        before = template[:placeholder_start]
        after = template[placeholder_end + len('        return response(404, {"message": "Endpoint not found"})'):] 
        
        if analysis["endpoints"] or analysis["is_websocket_handler"]:
            customized_template = before + routing_section + "\n        else:\n            return response(404, {\"message\": \"Endpoint not found\"})" + after
        else:
            customized_template = before + routing_section + after
    else:
        customized_template = template
    
    # Personalizar comentarios
    customized_template = customized_template.replace(
        'Handler principal de la función Lambda',
        f'Handler principal para {function_name}'
    )
    
    # Agregar funciones handler al final
    if handler_functions:
        customized_template += "\n\n# --------------------------\n# HANDLERS ESPECÍFICOS\n# --------------------------\n"
        customized_template += "\n".join(handler_functions)
    
    return customized_template

def generate_http_handler(func_name: str, method: str, path: str, function_name: str) -> str:
    """Genera una función handler HTTP específica"""
    
    # Determinar lógica básica según el path y método
    if "analytics" in path:
        return generate_analytics_handler(func_name, method, path)
    elif "recipe" in path:
        return generate_recipe_handler(func_name, method, path)
    elif "user" in path or "profile" in path:
        return generate_user_handler(func_name, method, path)
    elif "image" in path or "upload" in path:
        return generate_image_handler(func_name, method, path)
    elif "favorite" in path:
        return generate_favorite_handler(func_name, method, path)
    elif "notification" in path:
        return generate_notification_handler(func_name, method, path)
    else:
        return generate_generic_handler(func_name, method, path, function_name)

def generate_analytics_handler(func_name: str, method: str, path: str) -> str:
    """Genera handler para analytics"""
    if method == "POST" and "event" in path:
        return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Track analytics event"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    event_id = generate_id("event")
    
    required_fields = ["eventType", "eventData"]
    validation_error = validate_required_fields(data, required_fields)
    if validation_error:
        return response(400, {{"message": validation_error}})
    
    analytics_item = {{
        "PK": f"ANALYTICS#{{user_id}}",
        "SK": f"EVENT#{{event_id}}",
        "id": event_id,
        "userId": user_id,
        "eventType": data["eventType"],
        "eventData": data["eventData"],
        "timestamp": data.get("timestamp", get_current_timestamp()),
        "createdAt": get_current_timestamp()
    }}
    
    success = dynamo_put_item(analytics_item)
    if success:
        return response(201, {{"message": "Event tracked successfully", "event": analytics_item}})
    else:
        return response(500, {{"message": "Failed to track event"}})'''
    
    elif method == "GET" and "dashboard" in path:
        return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Get dashboard data"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    
    # Obtener eventos recientes
    try:
        params = {{
            "KeyConditionExpression": "PK = :pk AND begins_with(SK, :sk)",
            "ExpressionAttributeValues": {{
                ":pk": f"ANALYTICS#{{user_id}}",
                ":sk": "EVENT#"
            }},
            "ScanIndexForward": False,
            "Limit": 50
        }}
        
        response_data = table.query(**params)
        events = convert_decimals_to_floats(response_data.get("Items", []))
        
        # Procesar eventos para dashboard
        event_types = {{}}
        for event in events:
            event_type = event.get("eventType", "unknown")
            event_types[event_type] = event_types.get(event_type, 0) + 1
        
        return response(200, {{
            "totalEvents": len(events),
            "eventTypes": event_types,
            "recentEvents": events[:10]
        }})
        
    except Exception as e:
        print(f"Error getting dashboard data: {{e}}")
        return response(500, {{"message": "Failed to get dashboard data"}})'''
    
    else:
        return generate_generic_handler(func_name, method, path, "analytics")

def generate_recipe_handler(func_name: str, method: str, path: str) -> str:
    """Genera handler para recipes"""
    if method == "GET" and path == "/recipes":
        return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Get all recipes"""
    recipes = dynamo_query_gsi("GSI1", "RECIPE")
    return response(200, {{"recipes": recipes}})'''
    
    elif method == "POST" and path == "/recipes":
        return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Create new recipe"""
    required_fields = ["title", "description", "ingredients", "instructions"]
    validation_error = validate_required_fields(data, required_fields)
    if validation_error:
        return response(400, {{"message": validation_error}})
    
    recipe_id = generate_id("recipe")
    current_time = get_current_timestamp()
    
    recipe_item = {{
        "PK": f"RECIPE#{{recipe_id}}",
        "SK": "METADATA",
        "GSI1PK": "RECIPE",
        "GSI1SK": recipe_id,
        "id": recipe_id,
        "title": data["title"],
        "description": data["description"],
        "ingredients": data["ingredients"],
        "instructions": data["instructions"],
        "cookingTime": data.get("cookingTime"),
        "difficulty": data.get("difficulty"),
        "createdAt": current_time,
        "updatedAt": current_time
    }}
    
    success = dynamo_put_item(recipe_item)
    if success:
        return response(201, {{"message": "Recipe created successfully", "recipe": recipe_item}})
    else:
        return response(500, {{"message": "Failed to create recipe"}})'''
    
    else:
        return generate_generic_handler(func_name, method, path, "recipe")

def generate_user_handler(func_name: str, method: str, path: str) -> str:
    """Genera handler para user/profile"""
    return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - User/Profile operation"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    
    if "{method}" == "GET":
        user_data = dynamo_get_item(f"USER#{{user_id}}", "PROFILE")
        if not user_data:
            user_data = {{"id": user_id, "name": "Usuario Demo", "email": "<EMAIL>"}}
        return response(200, {{"user": user_data}})
    
    elif "{method}" == "PUT":
        # Actualizar perfil de usuario
        update_expression = "SET #name = :name, email = :email, updatedAt = :updatedAt"
        expression_values = {{
            ":name": data.get("name"),
            ":email": data.get("email"),
            ":updatedAt": get_current_timestamp()
        }}
        expression_names = {{"#name": "name"}}
        
        updated_user = dynamo_update_item(f"USER#{{user_id}}", "PROFILE", update_expression, expression_values, expression_names)
        if updated_user:
            return response(200, {{"message": "Profile updated successfully", "user": updated_user}})
        else:
            return response(500, {{"message": "Failed to update profile"}})
    
    else:
        return response(405, {{"message": "Method not allowed"}})'''

def generate_image_handler(func_name: str, method: str, path: str) -> str:
    """Genera handler para images/upload"""
    if "upload-url" in path:
        return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Generate upload URL"""
    required_fields = ["fileName", "fileType"]
    validation_error = validate_required_fields(data, required_fields)
    if validation_error:
        return response(400, {{"message": validation_error}})
    
    file_name = data["fileName"]
    file_type = data["fileType"]
    key = f"images/{{int(time.time() * 1000)}}-{{file_name}}"
    
    upload_url = s3_generate_presigned_url(BUCKET_NAME, key, file_type, 300)
    if upload_url:
        return response(200, {{
            "uploadUrl": upload_url,
            "key": key,
            "downloadUrl": f"https://{{BUCKET_NAME}}.s3.amazonaws.com/{{key}}"
        }})
    else:
        return response(500, {{"message": "Failed to generate upload URL"}})'''
    else:
        return generate_generic_handler(func_name, method, path, "image")

def generate_favorite_handler(func_name: str, method: str, path: str) -> str:
    """Genera handler para favorites"""
    return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Favorite operation"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    
    if "{method}" == "GET":
        # Obtener favoritos del usuario
        try:
            params = {{
                "KeyConditionExpression": "PK = :pk AND begins_with(SK, :sk)",
                "ExpressionAttributeValues": {{
                    ":pk": f"USER#{{user_id}}",
                    ":sk": "FAVORITE#"
                }}
            }}
            response_data = table.query(**params)
            favorites = convert_decimals_to_floats(response_data.get("Items", []))
            return response(200, {{"favorites": favorites}})
        except Exception as e:
            print(f"Error getting favorites: {{e}}")
            return response(500, {{"message": "Failed to get favorites"}})
    
    elif "{method}" == "POST":
        # Agregar a favoritos
        required_fields = ["itemId", "itemType"]
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return response(400, {{"message": validation_error}})
        
        favorite_id = generate_id("favorite")
        favorite_item = {{
            "PK": f"USER#{{user_id}}",
            "SK": f"FAVORITE#{{favorite_id}}",
            "id": favorite_id,
            "userId": user_id,
            "itemId": data["itemId"],
            "itemType": data["itemType"],
            "createdAt": get_current_timestamp()
        }}
        
        success = dynamo_put_item(favorite_item)
        if success:
            return response(201, {{"message": "Added to favorites", "favorite": favorite_item}})
        else:
            return response(500, {{"message": "Failed to add to favorites"}})
    
    else:
        return response(405, {{"message": "Method not allowed"}})'''

def generate_notification_handler(func_name: str, method: str, path: str) -> str:
    """Genera handler para notifications"""
    return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - Notification operation"""
    user_id = "user-123"  # TODO: Obtener del token de autenticación
    
    if "{method}" == "GET":
        # Obtener notificaciones del usuario
        try:
            params = {{
                "KeyConditionExpression": "PK = :pk AND begins_with(SK, :sk)",
                "ExpressionAttributeValues": {{
                    ":pk": f"USER#{{user_id}}",
                    ":sk": "NOTIFICATION#"
                }},
                "ScanIndexForward": False,
                "Limit": 50
            }}
            response_data = table.query(**params)
            notifications = convert_decimals_to_floats(response_data.get("Items", []))
            return response(200, {{"notifications": notifications}})
        except Exception as e:
            print(f"Error getting notifications: {{e}}")
            return response(500, {{"message": "Failed to get notifications"}})
    
    elif "{method}" == "POST":
        # Crear notificación
        required_fields = ["title", "message"]
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return response(400, {{"message": validation_error}})
        
        notification_id = generate_id("notification")
        notification_item = {{
            "PK": f"USER#{{user_id}}",
            "SK": f"NOTIFICATION#{{notification_id}}",
            "id": notification_id,
            "userId": user_id,
            "title": data["title"],
            "message": data["message"],
            "type": data.get("type", "info"),
            "read": False,
            "createdAt": get_current_timestamp()
        }}
        
        success = dynamo_put_item(notification_item)
        if success:
            return response(201, {{"message": "Notification created", "notification": notification_item}})
        else:
            return response(500, {{"message": "Failed to create notification"}})
    
    else:
        return response(405, {{"message": "Method not allowed"}})'''

def generate_generic_handler(func_name: str, method: str, path: str, function_type: str) -> str:
    """Genera handler genérico"""
    return f'''
def {func_name}(data: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """Handler para {method} {path} - {function_type} operation"""
    # TODO: Implementar lógica específica basada en el archivo Node.js original
    return response(200, {{
        "message": f"Endpoint {method} {path} ejecutado correctamente",
        "function_type": "{function_type}",
        "data": data,
        "path_params": path_params
    }})'''

def generate_websocket_connect_handler() -> str:
    """Genera handler para WebSocket connect"""
    return '''
def handle_websocket_connect(event, context):
    """Handler para WebSocket connect"""
    request_context = event.get("requestContext", {})
    connection_id = request_context.get("connectionId")
    user_id = event.get("queryStringParameters", {}).get("userId", "anonymous") if event.get("queryStringParameters") else "anonymous"
    
    if not connection_id:
        return {"statusCode": 400, "body": "Missing connection ID"}
    
    connection_item = {
        "PK": f"CONNECTION#{connection_id}",
        "SK": "METADATA",
        "connectionId": connection_id,
        "userId": user_id,
        "connectedAt": get_current_timestamp(),
        "status": "connected",
        "lastActivity": get_current_timestamp()
    }
    
    success = dynamo_put_item(connection_item)
    
    if success:
        print(f"User {user_id} connected with connection {connection_id}")
        return {"statusCode": 200, "body": "Connected"}
    else:
        return {"statusCode": 500, "body": "Failed to connect"}'''

def generate_websocket_disconnect_handler() -> str:
    """Genera handler para WebSocket disconnect"""
    return '''
def handle_websocket_disconnect(event, context):
    """Handler para WebSocket disconnect"""
    request_context = event.get("requestContext", {})
    connection_id = request_context.get("connectionId")
    
    if not connection_id:
        return {"statusCode": 400, "body": "Missing connection ID"}
    
    success = dynamo_delete_item(f"CONNECTION#{connection_id}", "METADATA")
    
    if success:
        print(f"Connection {connection_id} disconnected")
        return {"statusCode": 200, "body": "Disconnected"}
    else:
        return {"statusCode": 500, "body": "Failed to disconnect"}'''

def main():
    """Función principal"""
    print("🚀 Generando TODAS las funciones Python...")
    print("=" * 50)
    
    # Cargar datos de funciones
    functions_data = load_functions_data()
    print(f"📊 Encontradas {len(functions_data)} funciones para convertir")
    
    generated_count = 0
    
    for function_data in functions_data:
        function_name = function_data["FunctionName"]
        python_filename = f"{function_name}-python.py"
        
        # Solo generar si no existe ya
        if not os.path.exists(python_filename):
            print(f"\n📝 Generando: {python_filename}")
            
            # Analizar archivo Node.js
            analysis = analyze_nodejs_file(function_name)
            print(f"   🔍 Detectados: {len(analysis['endpoints'])} endpoints, WebSocket: {analysis['is_websocket_handler']}")
            
            # Generar código Python
            python_code = generate_python_code(function_name, analysis)
            
            # Escribir archivo
            with open(python_filename, 'w', encoding='utf-8') as f:
                f.write(python_code)
            
            generated_count += 1
            print(f"   ✅ Generado exitosamente")
        else:
            print(f"⏭️  {python_filename} ya existe, saltando...")
    
    print(f"\n🎉 Generación completada!")
    print(f"   📄 Archivos generados: {generated_count}")
    print(f"   📄 Archivos existentes: {len(functions_data) - generated_count}")
    print(f"   📄 Total: {len(functions_data)} funciones listas")

if __name__ == "__main__":
    main()
