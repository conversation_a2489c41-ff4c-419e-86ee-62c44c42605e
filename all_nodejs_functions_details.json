[{"FunctionName": "hopie-websocket-disconnect-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-websocket-disconnect-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 922, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:50:57.038+0000", "CodeLocation": null}, {"FunctionName": "hopie-couple-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-couple-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1764, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:27:07.962+0000", "CodeLocation": null}, {"FunctionName": "hopie-stats-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-stats-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 2251, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:28:07.905+0000", "CodeLocation": null}, {"FunctionName": "hopie-favorite-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-favorite-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1218, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:08.627+0000", "CodeLocation": null}, {"FunctionName": "hopie-search-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-search-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1201, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:04.420+0000", "CodeLocation": null}, {"FunctionName": "hopie-location-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-location-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1704, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:27:55.137+0000", "CodeLocation": null}, {"FunctionName": "hopie-message-history-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-message-history-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1453, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:51:00.970+0000", "CodeLocation": null}, {"FunctionName": "hopie-lifeplan-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-lifeplan-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1847, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:27:45.996+0000", "CodeLocation": null}, {"FunctionName": "hopie-notification-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-notification-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1389, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:00.321+0000", "CodeLocation": null}, {"FunctionName": "hopie-shopping-list-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-shopping-list-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1407, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:44:52.011+0000", "CodeLocation": null}, {"FunctionName": "hopie-user-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-user-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1342, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:44:39.070+0000", "CodeLocation": null}, {"FunctionName": "hopie-websocket-connect-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-websocket-connect-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 758, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:50:53.162+0000", "CodeLocation": null}, {"FunctionName": "hopie-scheduler-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-scheduler-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 2387, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T22:07:20.369+0000", "CodeLocation": null}, {"FunctionName": "hopie-places-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-places-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 2078, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:27:50.124+0000", "CodeLocation": null}, {"FunctionName": "hopie-image-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-image-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1638, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev", "BUCKET_NAME": "hopie-app-assets-dev-123456"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:21.014+0000", "CodeLocation": null}, {"FunctionName": "hopie-review-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-review-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1373, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:12.661+0000", "CodeLocation": null}, {"FunctionName": "hopie-tree-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-tree-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1798, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:27:12.766+0000", "CodeLocation": null}, {"FunctionName": "hopie-report-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-report-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1493, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:29.354+0000", "CodeLocation": null}, {"FunctionName": "hopie-chat-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-chat-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1846, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev", "WEBSOCKET_ENDPOINT": "wss://your-websocket-api-id.execute-api.us-east-2.amazonaws.com/dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:50:48.817+0000", "CodeLocation": null}, {"FunctionName": "hopie-questions-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-questions-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 2059, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T21:27:16.991+0000", "CodeLocation": null}, {"FunctionName": "hopie-recipe-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-recipe-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1463, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:44:43.764+0000", "CodeLocation": null}, {"FunctionName": "hopie-category-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-category-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1411, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:16.813+0000", "CodeLocation": null}, {"FunctionName": "hopie-analytics-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-analytics-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1535, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:45:25.048+0000", "CodeLocation": null}, {"FunctionName": "hopie-meal-plan-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-meal-plan-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1443, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:44:56.080+0000", "CodeLocation": null}, {"FunctionName": "hopie-ingredient-dev", "FunctionArn": "arn:aws:lambda:us-east-2:864899858226:function:hopie-ingredient-dev", "Runtime": "nodejs18.x", "Handler": "index.handler", "CodeSize": 1433, "MemorySize": 256, "Timeout": 30, "Environment": {"TABLE_NAME": "hopie-main-table-dev"}, "Role": "arn:aws:iam::864899858226:role/hopie-lambda-execution-role", "LastModified": "2025-07-11T19:44:47.776+0000", "CodeLocation": null}]