const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/meal-plans')) {
            return await getMealPlans(event);
        } else if (httpMethod === 'POST' && path.includes('/meal-plans')) {
            return await createMealPlan(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/meal-plan/')) {
            return await getMealPlanById(event);
        } else if (httpMethod === 'PUT' && path.includes('/meal-plan/')) {
            return await updateMealPlan(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/meal-plan/')) {
            return await deleteMealPlan(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getMealPlans(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'MEAL_PLAN#'
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            mealPlans: result.Items || []
        })
    };
}

async function createMealPlan(body, event) {
    const userId = 'user-123';
    const planId = `plan-${Date.now()}`;
    const { name, startDate, endDate, meals } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`,
            id: planId,
            name,
            startDate,
            endDate,
            meals: meals || {},
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Meal plan created successfully',
            mealPlan: params.Item
        })
    };
}

async function getMealPlanById(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'plan-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            mealPlan: result.Item || null
        })
    };
}

async function updateMealPlan(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'plan-123';
    const { name, startDate, endDate, meals } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`
        },
        UpdateExpression: 'SET #name = :name, startDate = :startDate, endDate = :endDate, meals = :meals, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':startDate': startDate,
            ':endDate': endDate,
            ':meals': meals,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Meal plan updated successfully',
            mealPlan: result.Attributes
        })
    };
}

async function deleteMealPlan(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'plan-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `MEAL_PLAN#${planId}`
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Meal plan deleted successfully'
        })
    };
}
