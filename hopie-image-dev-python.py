import json
import os
import time
import boto3
import decimal
from botocore.exceptions import ClientError
from typing import Dict, Any, Optional, List

# Leer variables de entorno
TABLE_NAME = os.environ.get("TABLE_NAME")
BUCKET_NAME = os.environ.get("BUCKET_NAME")

# Inicializar clientes AWS
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(TABLE_NAME) if TABLE_NAME else None
s3 = boto3.client("s3")

# Clase para manejar Decimals en JSON
class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o) if o % 1 != 0 else int(o)
        return super(DecimalEncoder, self).default(o)

def handler(event, context):
    """
    Handler principal para hopie-image-dev
    """
    print("Event received:", json.dumps(event, indent=2, cls=DecimalEncoder))

    http_method = event.get("httpMethod")
    path = event.get("path", "")
    headers = {k.lower(): v for k, v in event.get("headers", {}).items()}
    query_params = event.get("queryStringParameters") or {}
    path_params = event.get("pathParameters") or {}
    
    # Parseo robusto del body
    try:
        body_str = event.get("body", "{}")
        body = json.loads(body_str) if isinstance(body_str, str) and body_str else {}
    except json.JSONDecodeError:
        return response(400, {"message": "Invalid JSON format in request body"})

    # Enrutamiento de endpoints
    try:
        if http_method == "POST" and "/upload-url" in path:
            return handle_get_upload_url(body, path_params)
        elif http_method == "POST" and "/images" in path:
            return handle_save_image_metadata(body, path_params)
        elif http_method == "GET" and "/images" in path:
            return handle_get_images(query_params, path_params)
        elif http_method == "DELETE" and "/image/" in path:
            return handle_delete_image(query_params, path_params)
        else:
            return response(404, {"message": "Endpoint not found"})
        
    except Exception as e:
        print("Unhandled error:", str(e))
        return response(500, {"message": "Internal server error"})

# --------------------------
# FUNCIONES PRINCIPALES
# --------------------------

def handle_get_upload_url(body: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Genera una URL presignada para subir archivos a S3
    """
    # Validar campos requeridos
    validation_error = validate_required_fields(body, ["fileName", "fileType"])
    if validation_error:
        return response(400, {"message": validation_error})
    
    file_name = body["fileName"]
    file_type = body["fileType"]
    key = f"images/{int(time.time() * 1000)}-{file_name}"
    
    # Generar URL presignada
    upload_url = s3_generate_presigned_url(BUCKET_NAME, key, file_type, 300)
    
    if upload_url:
        download_url = f"https://{BUCKET_NAME}.s3.amazonaws.com/{key}"
        return response(200, {
            "uploadUrl": upload_url,
            "key": key,
            "downloadUrl": download_url
        })
    else:
        return response(500, {"message": "Failed to generate upload URL"})

def handle_save_image_metadata(body: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Guarda los metadatos de la imagen en DynamoDB
    """
    # Validar campos requeridos
    required_fields = ["key", "fileName", "fileType", "entityId", "entityType"]
    validation_error = validate_required_fields(body, required_fields)
    if validation_error:
        return response(400, {"message": validation_error})
    
    image_id = generate_id("image")
    current_time = get_current_timestamp()
    
    image_item = {
        "PK": f"IMAGE#{image_id}",
        "SK": "METADATA",
        "GSI1PK": f"{body['entityType']}#{body['entityId']}",
        "GSI1SK": f"IMAGE#{image_id}",
        "id": image_id,
        "key": body["key"],
        "fileName": body["fileName"],
        "fileType": body["fileType"],
        "entityId": body["entityId"],
        "entityType": body["entityType"],
        "url": f"https://{BUCKET_NAME}.s3.amazonaws.com/{body['key']}",
        "createdAt": current_time
    }
    
    success = dynamo_put_item(image_item)
    
    if success:
        return response(201, {
            "message": "Image metadata saved successfully",
            "image": image_item
        })
    else:
        return response(500, {"message": "Failed to save image metadata"})

def handle_get_images(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Obtiene las imágenes asociadas a una entidad
    """
    entity_id = query_params.get("entityId")
    entity_type = query_params.get("entityType")
    
    if not entity_id or not entity_type:
        return response(400, {"message": "entityId and entityType are required"})
    
    images = dynamo_query_gsi("GSI1", f"{entity_type}#{entity_id}", "IMAGE#")
    
    return response(200, {"images": images})

def handle_delete_image(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Elimina una imagen de S3 y DynamoDB
    """
    image_id = path_params.get("id", "image-123")
    
    # Obtener metadatos de la imagen primero
    image_data = dynamo_get_item(f"IMAGE#{image_id}", "METADATA")
    
    if image_data:
        # Eliminar de S3
        s3_success = s3_delete_object(BUCKET_NAME, image_data["key"])
        
        # Eliminar de DynamoDB
        dynamo_success = dynamo_delete_item(f"IMAGE#{image_id}", "METADATA")
        
        if s3_success and dynamo_success:
            return response(200, {"message": "Image deleted successfully"})
        else:
            return response(500, {"message": "Failed to delete image completely"})
    else:
        return response(404, {"message": "Image not found"})

# --------------------------
# FUNCIONES AUXILIARES
# --------------------------

def response(status_code: int, body_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Genera una respuesta HTTP estándar con headers CORS
    """
    return {
        "statusCode": status_code,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type,Authorization"
        },
        "body": json.dumps(body_dict, cls=DecimalEncoder)
    }

def convert_decimals_to_floats(obj):
    """
    Convierte objetos Decimal de DynamoDB a tipos nativos de Python
    """
    if isinstance(obj, list):
        return [convert_decimals_to_floats(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: convert_decimals_to_floats(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        return float(obj) if obj % 1 != 0 else int(obj)
    return obj

def get_current_timestamp() -> str:
    """
    Obtiene timestamp actual en formato ISO
    """
    return time.strftime('%Y-%m-%dT%H:%M:%S.%fZ', time.gmtime())

def generate_id(prefix: str) -> str:
    """
    Genera un ID único con prefijo
    """
    return f"{prefix}-{int(time.time() * 1000)}"

def s3_generate_presigned_url(bucket: str, key: str, content_type: str, expiration: int = 300) -> Optional[str]:
    """
    Genera una URL presignada para subir archivos a S3
    """
    try:
        url = s3.generate_presigned_url(
            'put_object',
            Params={'Bucket': bucket, 'Key': key, 'ContentType': content_type},
            ExpiresIn=expiration
        )
        return url
    except Exception as e:
        print(f"S3 presigned URL error: {str(e)}")
        return None

def s3_delete_object(bucket: str, key: str) -> bool:
    """
    Elimina un objeto de S3
    """
    try:
        s3.delete_object(Bucket=bucket, Key=key)
        return True
    except Exception as e:
        print(f"S3 delete error: {str(e)}")
        return False

def dynamo_get_item(pk: str, sk: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un item de DynamoDB
    """
    try:
        response = table.get_item(Key={"PK": pk, "SK": sk})
        item = response.get("Item")
        return convert_decimals_to_floats(item) if item else None
    except Exception as e:
        print(f"DynamoDB get error: {str(e)}")
        return None

def dynamo_put_item(item: Dict[str, Any]) -> bool:
    """
    Inserta un item en DynamoDB
    """
    try:
        table.put_item(Item=item)
        return True
    except Exception as e:
        print(f"DynamoDB put error: {str(e)}")
        return False

def dynamo_delete_item(pk: str, sk: str) -> bool:
    """
    Elimina un item de DynamoDB
    """
    try:
        table.delete_item(Key={"PK": pk, "SK": sk})
        return True
    except Exception as e:
        print(f"DynamoDB delete error: {str(e)}")
        return False

def dynamo_query_gsi(gsi_name: str, gsi_pk: str, gsi_sk_prefix: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Realiza una query en un GSI
    """
    try:
        params = {
            "IndexName": gsi_name,
            "KeyConditionExpression": "GSI1PK = :gsi1pk",
            "ExpressionAttributeValues": {":gsi1pk": gsi_pk}
        }
        
        if gsi_sk_prefix:
            params["KeyConditionExpression"] += " AND begins_with(GSI1SK, :gsi1sk)"
            params["ExpressionAttributeValues"][":gsi1sk"] = gsi_sk_prefix
            
        response = table.query(**params)
        return convert_decimals_to_floats(response.get("Items", []))
    except Exception as e:
        print(f"DynamoDB query error: {str(e)}")
        return []

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
    """
    Valida que todos los campos requeridos estén presentes
    """
    if not isinstance(data, dict):
        return "Request body must be a JSON object"
    
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        return f"Missing required fields: {', '.join(missing_fields)}"
    
    return None
