const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/places')) {
            return await getPlaces(event);
        } else if (httpMethod === 'POST' && path.includes('/places')) {
            return await createPlace(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/place/')) {
            return await getPlaceById(event);
        } else if (httpMethod === 'PUT' && path.includes('/place/')) {
            return await updatePlace(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/place/')) {
            return await deletePlace(event);
        } else if (httpMethod === 'GET' && path.includes('/places/nearby')) {
            return await getNearbyPlaces(event);
        } else if (httpMethod === 'POST' && path.includes('/place/visit')) {
            return await markAsVisited(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getPlaces(event) {
    const category = event.queryStringParameters?.category || 'all';

    let params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'PLACE'
        }
    };

    if (category !== 'all') {
        params.FilterExpression = 'category = :category';
        params.ExpressionAttributeValues[':category'] = category;
    }

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            places: result.Items || []
        })
    };
}

async function createPlace(body, event) {
    const placeId = `place-${Date.now()}`;
    const { name, description, category, address, coordinates, website, phone } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA',
            GSI1PK: 'PLACE',
            GSI1SK: placeId,
            id: placeId,
            name,
            description,
            category,
            address,
            coordinates: coordinates || { lat: 0, lng: 0 },
            website,
            phone,
            rating: 0,
            reviewCount: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place created successfully',
            place: params.Item
        })
    };
}

async function getPlaceById(event) {
    const placeId = event.pathParameters?.id || 'place-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            place: result.Item || null
        })
    };
}

async function updatePlace(body, event) {
    const placeId = event.pathParameters?.id || 'place-123';
    const { name, description, category, address, coordinates, website, phone } = body;

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA'
        },
        UpdateExpression: 'SET #name = :name, description = :description, category = :category, address = :address, coordinates = :coordinates, website = :website, phone = :phone, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':description': description,
            ':category': category,
            ':address': address,
            ':coordinates': coordinates,
            ':website': website,
            ':phone': phone,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place updated successfully',
            place: result.Attributes
        })
    };
}

async function deletePlace(event) {
    const placeId = event.pathParameters?.id || 'place-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `PLACE#${placeId}`,
            SK: 'METADATA'
        }
    };

    await dynamodb.delete(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place deleted successfully'
        })
    };
}

async function getNearbyPlaces(event) {
    const lat = parseFloat(event.queryStringParameters?.lat || '0');
    const lng = parseFloat(event.queryStringParameters?.lng || '0');
    const radius = parseFloat(event.queryStringParameters?.radius || '10');

    const params = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'PLACE'
        }
    };

    const result = await dynamodb.query(params).promise();

    const nearbyPlaces = (result.Items || []).filter(place => {
        if (!place.coordinates) return false;
        const distance = calculateDistance(lat, lng, place.coordinates.lat, place.coordinates.lng);
        return distance <= radius;
    });

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            places: nearbyPlaces,
            searchCenter: { lat, lng },
            radius
        })
    };
}

async function markAsVisited(body, event) {
    const userId = 'user-123';
    const { placeId, visitDate, notes } = body;
    const visitId = `visit-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `VISIT#${visitId}`,
            id: visitId,
            userId,
            placeId,
            visitDate: visitDate || new Date().toISOString(),
            notes,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Place marked as visited successfully',
            visit: params.Item
        })
    };
}

function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371;
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}
