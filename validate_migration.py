#!/usr/bin/env python3
"""
Script para validar que las funciones Lambda migradas funcionan correctamente
"""

import json
import boto3
import time
from typing import Dict, List, Any, Optional
from botocore.exceptions import ClientError

# Configuración
REGION = "us-east-2"

def load_functions_data() -> List[Dict[str, Any]]:
    """
    Carga los datos de las funciones desde el archivo JSON
    """
    try:
        with open("all_nodejs_functions_details.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: No se encontró el archivo all_nodejs_functions_details.json")
        return []

def test_lambda_function(function_name: str) -> Dict[str, Any]:
    """
    Prueba una función Lambda con un evento de prueba básico
    """
    lambda_client = boto3.client('lambda', region_name=REGION)
    
    # Evento de prueba básico para API Gateway
    test_event = {
        "httpMethod": "GET",
        "path": "/test",
        "headers": {
            "Content-Type": "application/json"
        },
        "queryStringParameters": {},
        "pathParameters": {},
        "body": None
    }
    
    try:
        print(f"   🧪 Probando función {function_name}...")
        
        response = lambda_client.invoke(
            FunctionName=function_name,
            InvocationType='RequestResponse',
            Payload=json.dumps(test_event)
        )
        
        # Leer respuesta
        payload = response['Payload'].read()
        result = json.loads(payload)
        
        # Verificar si hay errores
        if 'errorMessage' in result:
            return {
                "success": False,
                "error": result['errorMessage'],
                "status_code": response.get('StatusCode', 500)
            }
        
        # Verificar respuesta HTTP válida
        if isinstance(result, dict) and 'statusCode' in result:
            return {
                "success": True,
                "status_code": result['statusCode'],
                "response": result
            }
        
        return {
            "success": True,
            "status_code": 200,
            "response": result
        }
        
    except ClientError as e:
        return {
            "success": False,
            "error": f"AWS Error: {e}",
            "status_code": 500
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"General Error: {e}",
            "status_code": 500
        }

def check_function_configuration(function_name: str) -> Dict[str, Any]:
    """
    Verifica la configuración de una función Lambda
    """
    lambda_client = boto3.client('lambda', region_name=REGION)
    
    try:
        response = lambda_client.get_function_configuration(FunctionName=function_name)
        
        return {
            "success": True,
            "runtime": response.get('Runtime'),
            "handler": response.get('Handler'),
            "memory_size": response.get('MemorySize'),
            "timeout": response.get('Timeout'),
            "environment": response.get('Environment', {}).get('Variables', {}),
            "last_modified": response.get('LastModified')
        }
        
    except ClientError as e:
        return {
            "success": False,
            "error": f"Error obteniendo configuración: {e}"
        }

def main():
    """
    Función principal del script de validación
    """
    print("🔍 Iniciando validación de migración Lambda")
    print("=" * 50)

    # Cargar datos de funciones
    functions_data = load_functions_data()
    if not functions_data:
        print("❌ No se pudieron cargar los datos de las funciones")
        return

    successful = 0
    failed = 0

    print(f"\n📊 Validando {len(functions_data)} funciones...")

    for function_data in functions_data:
        function_name = function_data["FunctionName"]
        print(f"\n📋 Validando: {function_name}")

        # Verificar configuración
        config = check_function_configuration(function_name)

        if config["success"]:
            print(f"   ✅ Runtime: {config['runtime']}")
            print(f"   ✅ Handler: {config['handler']}")

            # Probar función
            test_result = test_lambda_function(function_name)

            if test_result["success"]:
                print(f"   ✅ Prueba exitosa - Status: {test_result['status_code']}")
                successful += 1
            else:
                print(f"   ❌ Prueba falló: {test_result['error']}")
                failed += 1
        else:
            print(f"   ❌ Error en configuración: {config['error']}")
            failed += 1

    # Resumen
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE VALIDACIÓN:")
    print(f"   ✅ Funciones exitosas: {successful}")
    print(f"   ❌ Funciones con errores: {failed}")
    print(f"   📈 Total: {len(functions_data)}")

    if failed == 0:
        print("\n🎉 ¡Todas las funciones pasaron la validación!")
    else:
        print(f"\n⚠️  {failed} funciones requieren atención")

if __name__ == "__main__":
    main()

if __name__ == "__main__":
    main()
