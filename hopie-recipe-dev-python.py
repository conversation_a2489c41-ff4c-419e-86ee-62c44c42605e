import json
import os
import time
import boto3
import decimal
from botocore.exceptions import ClientError
from typing import Dict, Any, Optional, List

# Leer variables de entorno
TABLE_NAME = os.environ.get("TABLE_NAME")

# Inicializar clientes AWS
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(TABLE_NAME) if TABLE_NAME else None

# Clase para manejar Decimals en JSON
class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o) if o % 1 != 0 else int(o)
        return super(DecimalEncoder, self).default(o)

def handler(event, context):
    """
    Handler principal para hopie-recipe-dev
    """
    print("Event received:", json.dumps(event, indent=2, cls=DecimalEncoder))

    http_method = event.get("httpMethod")
    path = event.get("path", "")
    headers = {k.lower(): v for k, v in event.get("headers", {}).items()}
    query_params = event.get("queryStringParameters") or {}
    path_params = event.get("pathParameters") or {}
    
    # Parseo robusto del body
    try:
        body_str = event.get("body", "{}")
        body = json.loads(body_str) if isinstance(body_str, str) and body_str else {}
    except json.JSONDecodeError:
        return response(400, {"message": "Invalid JSON format in request body"})

    # Enrutamiento de endpoints
    try:
        if http_method == "GET" and "/recipes" in path:
            return handle_get_recipes(query_params, path_params)
        elif http_method == "POST" and "/recipes" in path:
            return handle_create_recipe(body, path_params)
        elif http_method == "GET" and "/recipe/" in path:
            return handle_get_recipe_by_id(query_params, path_params)
        elif http_method == "PUT" and "/recipe/" in path:
            return handle_update_recipe(body, path_params)
        elif http_method == "DELETE" and "/recipe/" in path:
            return handle_delete_recipe(query_params, path_params)
        else:
            return response(404, {"message": "Endpoint not found"})
        
    except Exception as e:
        print("Unhandled error:", str(e))
        return response(500, {"message": "Internal server error"})

# --------------------------
# FUNCIONES PRINCIPALES
# --------------------------

def handle_get_recipes(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Obtiene todas las recetas
    """
    recipes = dynamo_query_gsi("GSI1", "RECIPE")
    return response(200, {"recipes": recipes})

def handle_create_recipe(body: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Crea una nueva receta
    """
    # Validar campos requeridos
    required_fields = ["title", "description", "ingredients", "instructions", "cookingTime", "difficulty"]
    validation_error = validate_required_fields(body, required_fields)
    if validation_error:
        return response(400, {"message": validation_error})
    
    recipe_id = generate_id("recipe")
    current_time = get_current_timestamp()
    
    recipe_item = {
        "PK": f"RECIPE#{recipe_id}",
        "SK": "METADATA",
        "GSI1PK": "RECIPE",
        "GSI1SK": recipe_id,
        "id": recipe_id,
        "title": body["title"],
        "description": body["description"],
        "ingredients": body["ingredients"],
        "instructions": body["instructions"],
        "cookingTime": body["cookingTime"],
        "difficulty": body["difficulty"],
        "createdAt": current_time,
        "updatedAt": current_time
    }
    
    success = dynamo_put_item(recipe_item)
    
    if success:
        return response(201, {
            "message": "Recipe created successfully",
            "recipe": recipe_item
        })
    else:
        return response(500, {"message": "Failed to create recipe"})

def handle_get_recipe_by_id(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Obtiene una receta por ID
    """
    recipe_id = path_params.get("id", "recipe-123")
    
    recipe_data = dynamo_get_item(f"RECIPE#{recipe_id}", "METADATA")
    
    return response(200, {"recipe": recipe_data})

def handle_update_recipe(body: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Actualiza una receta existente
    """
    recipe_id = path_params.get("id", "recipe-123")
    
    # Validar campos requeridos
    required_fields = ["title", "description", "ingredients", "instructions", "cookingTime", "difficulty"]
    validation_error = validate_required_fields(body, required_fields)
    if validation_error:
        return response(400, {"message": validation_error})
    
    # Actualizar en DynamoDB
    update_expression = ("SET title = :title, description = :description, ingredients = :ingredients, "
                        "instructions = :instructions, cookingTime = :cookingTime, difficulty = :difficulty, "
                        "updatedAt = :updatedAt")
    
    expression_values = {
        ":title": body["title"],
        ":description": body["description"],
        ":ingredients": body["ingredients"],
        ":instructions": body["instructions"],
        ":cookingTime": body["cookingTime"],
        ":difficulty": body["difficulty"],
        ":updatedAt": get_current_timestamp()
    }
    
    updated_recipe = dynamo_update_item(
        f"RECIPE#{recipe_id}", 
        "METADATA", 
        update_expression, 
        expression_values
    )
    
    if updated_recipe:
        return response(200, {
            "message": "Recipe updated successfully",
            "recipe": updated_recipe
        })
    else:
        return response(500, {"message": "Failed to update recipe"})

def handle_delete_recipe(query_params: Dict[str, Any], path_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Elimina una receta
    """
    recipe_id = path_params.get("id", "recipe-123")
    
    success = dynamo_delete_item(f"RECIPE#{recipe_id}", "METADATA")
    
    if success:
        return response(200, {"message": "Recipe deleted successfully"})
    else:
        return response(500, {"message": "Failed to delete recipe"})

# --------------------------
# FUNCIONES AUXILIARES
# --------------------------

def response(status_code: int, body_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Genera una respuesta HTTP estándar con headers CORS
    """
    return {
        "statusCode": status_code,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type,Authorization"
        },
        "body": json.dumps(body_dict, cls=DecimalEncoder)
    }

def convert_decimals_to_floats(obj):
    """
    Convierte objetos Decimal de DynamoDB a tipos nativos de Python
    """
    if isinstance(obj, list):
        return [convert_decimals_to_floats(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: convert_decimals_to_floats(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        return float(obj) if obj % 1 != 0 else int(obj)
    return obj

def get_current_timestamp() -> str:
    """
    Obtiene timestamp actual en formato ISO
    """
    return time.strftime('%Y-%m-%dT%H:%M:%S.%fZ', time.gmtime())

def generate_id(prefix: str) -> str:
    """
    Genera un ID único con prefijo
    """
    return f"{prefix}-{int(time.time() * 1000)}"

def dynamo_get_item(pk: str, sk: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un item de DynamoDB
    """
    try:
        response = table.get_item(Key={"PK": pk, "SK": sk})
        item = response.get("Item")
        return convert_decimals_to_floats(item) if item else None
    except Exception as e:
        print(f"DynamoDB get error: {str(e)}")
        return None

def dynamo_put_item(item: Dict[str, Any]) -> bool:
    """
    Inserta un item en DynamoDB
    """
    try:
        table.put_item(Item=item)
        return True
    except Exception as e:
        print(f"DynamoDB put error: {str(e)}")
        return False

def dynamo_update_item(pk: str, sk: str, update_expression: str, 
                      expression_values: Dict[str, Any], 
                      expression_names: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
    """
    Actualiza un item en DynamoDB
    """
    try:
        params = {
            "Key": {"PK": pk, "SK": sk},
            "UpdateExpression": update_expression,
            "ExpressionAttributeValues": expression_values,
            "ReturnValues": "ALL_NEW"
        }
        if expression_names:
            params["ExpressionAttributeNames"] = expression_names
            
        response = table.update_item(**params)
        return convert_decimals_to_floats(response.get("Attributes"))
    except Exception as e:
        print(f"DynamoDB update error: {str(e)}")
        return None

def dynamo_delete_item(pk: str, sk: str) -> bool:
    """
    Elimina un item de DynamoDB
    """
    try:
        table.delete_item(Key={"PK": pk, "SK": sk})
        return True
    except Exception as e:
        print(f"DynamoDB delete error: {str(e)}")
        return False

def dynamo_query_gsi(gsi_name: str, gsi_pk: str, gsi_sk_prefix: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Realiza una query en un GSI
    """
    try:
        params = {
            "IndexName": gsi_name,
            "KeyConditionExpression": "GSI1PK = :gsi1pk",
            "ExpressionAttributeValues": {":gsi1pk": gsi_pk}
        }
        
        if gsi_sk_prefix:
            params["KeyConditionExpression"] += " AND begins_with(GSI1SK, :gsi1sk)"
            params["ExpressionAttributeValues"][":gsi1sk"] = gsi_sk_prefix
            
        response = table.query(**params)
        return convert_decimals_to_floats(response.get("Items", []))
    except Exception as e:
        print(f"DynamoDB query error: {str(e)}")
        return []

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
    """
    Valida que todos los campos requeridos estén presentes
    """
    if not isinstance(data, dict):
        return "Request body must be a JSON object"
    
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        return f"Missing required fields: {', '.join(missing_fields)}"
    
    return None
