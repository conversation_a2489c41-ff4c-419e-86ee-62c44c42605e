# 🚀 Migración Automática de Lambda Functions: Node.js → Python

Este proyecto automatiza la migración completa de todas tus funciones Lambda de Node.js a Python, manteniendo la misma funcionalidad y estructura.

## 📋 Archivos Generados

### 🔧 Scripts Principales
- **`migrate_lambdas_to_python.py`** - Script principal de migración automatizada
- **`validate_migration.py`** - Script de validación post-migración
- **`requirements.txt`** - Dependencias Python para las funciones Lambda

### 📄 Plantillas y Conversiones
- **`lambda_python_template.py`** - Plantilla base para funciones Python
- **`hopie-user-dev-python.py`** - Conversión específica para gestión de usuarios
- **`hopie-recipe-dev-python.py`** - Conversión específica para gestión de recetas
- **`hopie-image-dev-python.py`** - Conversión específica para gestión de imágenes (incluye S3)

## 🎯 ¿Qué hace la migración?

### ✅ Cambios Automáticos
1. **Runtime**: `nodejs18.x` → `python3.9`
2. **Handler**: `index.handler` → `index.handler` (mantiene el mismo nombre)
3. **Código**: Reemplaza `index.js` con `index.py` equivalente
4. **Funcionalidad**: Mantiene todos los endpoints y lógica de negocio
5. **Variables de entorno**: Conserva todas las configuraciones existentes

### 🔄 Proceso de Migración
1. **Análisis**: Examina cada función Node.js para detectar endpoints y dependencias
2. **Generación**: Crea código Python equivalente automáticamente
3. **Empaquetado**: Crea archivos ZIP con el código Python y dependencias
4. **Despliegue**: Actualiza las funciones Lambda en AWS
5. **Validación**: Verifica que todo funcione correctamente

## 🚀 Instrucciones de Uso

### Paso 1: Preparación
```bash
# Asegúrate de tener AWS CLI configurado
aws configure

# Instala las dependencias Python necesarias
pip install boto3
```

### Paso 2: Ejecutar Migración
```bash
# Ejecuta el script principal de migración
python migrate_lambdas_to_python.py
```

El script te mostrará:
- ✅ Número de funciones detectadas
- 📝 Archivos Python que se van a generar
- ⚠️ Confirmación antes de proceder
- 📊 Progreso de cada función
- 🎉 Resumen final

### Paso 3: Validación
```bash
# Valida que todas las funciones migraron correctamente
python validate_migration.py
```

## 📊 Funciones Incluidas

El script migrará automáticamente estas **24 funciones Lambda**:

| Función | ARN | Variables de Entorno |
|---------|-----|---------------------|
| hopie-analytics-dev | `arn:aws:lambda:us-east-2:************:function:hopie-analytics-dev` | TABLE_NAME |
| hopie-category-dev | `arn:aws:lambda:us-east-2:************:function:hopie-category-dev` | TABLE_NAME |
| hopie-chat-dev | `arn:aws:lambda:us-east-2:************:function:hopie-chat-dev` | TABLE_NAME, WEBSOCKET_ENDPOINT |
| hopie-couple-dev | `arn:aws:lambda:us-east-2:************:function:hopie-couple-dev` | TABLE_NAME |
| hopie-favorite-dev | `arn:aws:lambda:us-east-2:************:function:hopie-favorite-dev` | TABLE_NAME |
| hopie-image-dev | `arn:aws:lambda:us-east-2:************:function:hopie-image-dev` | TABLE_NAME, BUCKET_NAME |
| hopie-ingredient-dev | `arn:aws:lambda:us-east-2:************:function:hopie-ingredient-dev` | TABLE_NAME |
| hopie-lifeplan-dev | `arn:aws:lambda:us-east-2:************:function:hopie-lifeplan-dev` | TABLE_NAME |
| hopie-location-dev | `arn:aws:lambda:us-east-2:************:function:hopie-location-dev` | TABLE_NAME |
| hopie-meal-plan-dev | `arn:aws:lambda:us-east-2:************:function:hopie-meal-plan-dev` | TABLE_NAME |
| hopie-message-history-dev | `arn:aws:lambda:us-east-2:************:function:hopie-message-history-dev` | TABLE_NAME |
| hopie-notification-dev | `arn:aws:lambda:us-east-2:************:function:hopie-notification-dev` | TABLE_NAME |
| hopie-places-dev | `arn:aws:lambda:us-east-2:************:function:hopie-places-dev` | TABLE_NAME |
| hopie-questions-dev | `arn:aws:lambda:us-east-2:************:function:hopie-questions-dev` | TABLE_NAME |
| hopie-recipe-dev | `arn:aws:lambda:us-east-2:************:function:hopie-recipe-dev` | TABLE_NAME |
| hopie-report-dev | `arn:aws:lambda:us-east-2:************:function:hopie-report-dev` | TABLE_NAME |
| hopie-review-dev | `arn:aws:lambda:us-east-2:************:function:hopie-review-dev` | TABLE_NAME |
| hopie-scheduler-dev | `arn:aws:lambda:us-east-2:************:function:hopie-scheduler-dev` | TABLE_NAME |
| hopie-search-dev | `arn:aws:lambda:us-east-2:************:function:hopie-search-dev` | TABLE_NAME |
| hopie-shopping-list-dev | `arn:aws:lambda:us-east-2:************:function:hopie-shopping-list-dev` | TABLE_NAME |
| hopie-stats-dev | `arn:aws:lambda:us-east-2:************:function:hopie-stats-dev` | TABLE_NAME |
| hopie-tree-dev | `arn:aws:lambda:us-east-2:************:function:hopie-tree-dev` | TABLE_NAME |
| hopie-user-dev | `arn:aws:lambda:us-east-2:************:function:hopie-user-dev` | TABLE_NAME |
| hopie-websocket-connect-dev | `arn:aws:lambda:us-east-2:************:function:hopie-websocket-connect-dev` | TABLE_NAME |
| hopie-websocket-disconnect-dev | `arn:aws:lambda:us-east-2:************:function:hopie-websocket-disconnect-dev` | TABLE_NAME |

## 🔧 Características Técnicas

### 🐍 Código Python Generado
- **Estructura modular**: Funciones auxiliares reutilizables
- **Manejo de errores**: Try-catch robusto para todas las operaciones
- **CORS**: Headers configurados automáticamente
- **DynamoDB**: Conversión automática de Decimals
- **S3**: Soporte para funciones que usan almacenamiento
- **Logging**: Logs detallados para debugging

### 🔄 Equivalencias Node.js → Python
| Node.js | Python |
|---------|--------|
| `AWS.DynamoDB.DocumentClient()` | `boto3.resource("dynamodb")` |
| `JSON.parse(body)` | `json.loads(body)` |
| `new Date().toISOString()` | `time.strftime('%Y-%m-%dT%H:%M:%S.%fZ')` |
| `Date.now()` | `int(time.time() * 1000)` |
| `s3.getSignedUrl()` | `s3.generate_presigned_url()` |

## ⚠️ Consideraciones Importantes

### 🔒 Seguridad
- ✅ Mantiene todos los roles y permisos IAM existentes
- ✅ Conserva todas las variables de entorno
- ✅ No modifica configuraciones de red o VPC

### 🔄 Reversibilidad
- ✅ Los archivos Node.js originales permanecen en las carpetas locales
- ✅ Puedes revertir manualmente si es necesario
- ✅ El script no elimina código, solo actualiza las funciones Lambda

### 📊 Monitoreo
- ✅ Usa CloudWatch para monitorear las funciones migradas
- ✅ Los logs mantienen el mismo formato
- ✅ Las métricas se conservan

## 🆘 Solución de Problemas

### Error: "Function not found"
```bash
# Verifica que AWS CLI esté configurado correctamente
aws lambda list-functions --region us-east-2
```

### Error: "Access denied"
```bash
# Verifica permisos IAM para Lambda
aws iam get-user
```

### Error en validación
```bash
# Ejecuta validación individual
python validate_migration.py
```

## 🎉 ¡Listo!

Una vez completada la migración:
1. ✅ Todas tus funciones Lambda estarán en Python 3.9
2. ✅ Mantendrán la misma funcionalidad
3. ✅ Los endpoints seguirán funcionando igual
4. ✅ Las variables de entorno se conservarán
5. ✅ El rendimiento será similar o mejor

**¡Tu aplicación seguirá funcionando exactamente igual, pero ahora en Python!** 🐍✨
