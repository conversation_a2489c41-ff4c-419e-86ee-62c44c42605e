#!/usr/bin/env python3
"""
🚀 SCRIPT PRINCIPAL DE MIGRACIÓN LAMBDA: NODE.JS → PYTHON
Ejecuta este archivo para migrar automáticamente todas tus funciones Lambda
"""

import os
import sys
import subprocess
import json
from typing import List, Dict, Any

def print_banner():
    """Muestra el banner de bienvenida"""
    print("=" * 70)
    print("🚀 MIGRACIÓN AUTOMÁTICA DE LAMBDA FUNCTIONS")
    print("   Node.js → Python")
    print("=" * 70)
    print()

def check_prerequisites() -> bool:
    """Verifica que todos los prerequisitos estén instalados"""
    print("🔍 Verificando prerequisitos...")
    
    # Verificar Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
            print("❌ Se requiere Python 3.7 o superior")
            return False
        print(f"   ✅ Python {python_version.major}.{python_version.minor}")
    except Exception:
        print("❌ Error verificando versión de Python")
        return False
    
    # Verificar AWS CLI
    try:
        result = subprocess.run(['aws', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ AWS CLI instalado")
        else:
            print("❌ AWS CLI no encontrado. Instala AWS CLI primero.")
            return False
    except FileNotFoundError:
        print("❌ AWS CLI no encontrado. Instala AWS CLI primero.")
        return False
    
    # Verificar configuración AWS
    try:
        result = subprocess.run(['aws', 'sts', 'get-caller-identity'], capture_output=True, text=True)
        if result.returncode == 0:
            identity = json.loads(result.stdout)
            print(f"   ✅ AWS configurado - Account: {identity.get('Account', 'N/A')}")
        else:
            print("❌ AWS no configurado. Ejecuta 'aws configure' primero.")
            return False
    except Exception:
        print("❌ Error verificando configuración AWS")
        return False
    
    # Verificar boto3
    try:
        import boto3
        print("   ✅ boto3 disponible")
    except ImportError:
        print("❌ boto3 no encontrado. Instala con: pip install boto3")
        return False
    
    # Verificar archivo de funciones
    if not os.path.exists("all_nodejs_functions_details.json"):
        print("❌ Archivo 'all_nodejs_functions_details.json' no encontrado")
        return False
    print("   ✅ Archivo de funciones encontrado")
    
    return True

def load_and_show_functions() -> List[Dict[str, Any]]:
    """Carga y muestra las funciones que serán migradas"""
    try:
        with open("all_nodejs_functions_details.json", "r") as f:
            functions = json.load(f)
        
        print(f"\n📊 FUNCIONES DETECTADAS: {len(functions)}")
        print("-" * 50)
        
        for i, func in enumerate(functions, 1):
            name = func["FunctionName"]
            runtime = func["Runtime"]
            print(f"   {i:2d}. {name} ({runtime})")
        
        return functions
    except Exception as e:
        print(f"❌ Error cargando funciones: {e}")
        return []

def confirm_migration(functions: List[Dict[str, Any]]) -> bool:
    """Solicita confirmación del usuario"""
    print(f"\n⚠️  ATENCIÓN:")
    print(f"   • Se migrarán {len(functions)} funciones Lambda")
    print(f"   • Runtime cambiará de 'nodejs18.x' a 'python3.9'")
    print(f"   • El código se reemplazará con versión Python equivalente")
    print(f"   • El handler cambiará a 'index.handler'")
    print(f"   • Las variables de entorno se mantendrán")
    print()
    print("🔄 PROCESO:")
    print("   1. Generar código Python para cada función")
    print("   2. Crear paquetes de despliegue")
    print("   3. Actualizar funciones en AWS Lambda")
    print("   4. Validar que todo funcione correctamente")
    print()
    
    while True:
        response = input("¿Deseas continuar con la migración? (s/N): ").lower().strip()
        if response in ['s', 'si', 'sí', 'y', 'yes']:
            return True
        elif response in ['n', 'no', ''] or not response:
            return False
        else:
            print("Por favor responde 's' para sí o 'n' para no.")

def run_migration() -> bool:
    """Ejecuta el script de migración"""
    print("\n🚀 INICIANDO MIGRACIÓN...")
    print("=" * 50)
    
    try:
        # Ejecutar script de migración
        result = subprocess.run([sys.executable, 'migrate_lambdas_to_python.py'], 
                              capture_output=False, text=True)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error ejecutando migración: {e}")
        return False

def run_validation() -> bool:
    """Ejecuta el script de validación"""
    print("\n🔍 INICIANDO VALIDACIÓN...")
    print("=" * 50)
    
    try:
        # Ejecutar script de validación
        result = subprocess.run([sys.executable, 'validate_migration.py'], 
                              capture_output=False, text=True)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error ejecutando validación: {e}")
        return False

def show_final_summary():
    """Muestra el resumen final"""
    print("\n" + "=" * 70)
    print("🎉 MIGRACIÓN COMPLETADA")
    print("=" * 70)
    print()
    print("✅ CAMBIOS REALIZADOS:")
    print("   • Runtime: nodejs18.x → python3.9")
    print("   • Handler: index.handler → index.handler")
    print("   • Código: index.js → index.py")
    print("   • Funcionalidad: Mantenida 100%")
    print()
    print("📊 PRÓXIMOS PASOS:")
    print("   1. Monitorea las funciones en AWS CloudWatch")
    print("   2. Prueba los endpoints de tu aplicación")
    print("   3. Verifica que todo funcione como antes")
    print()
    print("🆘 SI HAY PROBLEMAS:")
    print("   • Revisa los logs de CloudWatch")
    print("   • Ejecuta: python validate_migration.py")
    print("   • Los archivos Node.js originales están en las carpetas locales")
    print()
    print("🐍 ¡Tu aplicación ahora funciona con Python! ✨")

def main():
    """Función principal"""
    print_banner()
    
    # Verificar prerequisitos
    if not check_prerequisites():
        print("\n❌ No se pueden cumplir los prerequisitos. Revisa los errores anteriores.")
        sys.exit(1)
    
    # Cargar y mostrar funciones
    functions = load_and_show_functions()
    if not functions:
        print("\n❌ No se pudieron cargar las funciones.")
        sys.exit(1)
    
    # Confirmar migración
    if not confirm_migration(functions):
        print("\n❌ Migración cancelada por el usuario.")
        sys.exit(0)
    
    # Ejecutar migración
    migration_success = run_migration()
    if not migration_success:
        print("\n❌ La migración falló. Revisa los errores anteriores.")
        sys.exit(1)
    
    # Ejecutar validación
    validation_success = run_validation()
    if not validation_success:
        print("\n⚠️  La validación encontró problemas. Revisa los resultados.")
    
    # Mostrar resumen final
    show_final_summary()
    
    print(f"\n{'🎉 ÉXITO TOTAL' if validation_success else '⚠️  COMPLETADO CON ADVERTENCIAS'}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Migración interrumpida por el usuario.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
