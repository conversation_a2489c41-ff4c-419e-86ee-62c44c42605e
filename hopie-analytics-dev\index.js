const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/analytics/event')) {
            return await trackEvent(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/analytics/dashboard')) {
            return await getDashboardData(event);
        } else if (httpMethod === 'GET' && path.includes('/analytics/user-stats')) {
            return await getUserStats(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function trackEvent(body, event) {
    const userId = 'user-123';
    const eventId = `event-${Date.now()}`;
    const { eventType, eventData, timestamp } = body;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ANALYTICS#${userId}`,
            SK: `EVENT#${eventId}`,
            id: eventId,
            userId,
            eventType,
            eventData,
            timestamp: timestamp || new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Event tracked successfully',
            event: params.Item
        })
    };
}

async function getDashboardData(event) {
    const userId = 'user-123';

    // Get recent events
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ANALYTICS#${userId}`,
            ':sk': 'EVENT#'
        },
        ScanIndexForward: false,
        Limit: 50
    };

    const result = await dynamodb.query(params).promise();

    // Process events for dashboard
    const events = result.Items || [];
    const eventTypes = {};

    events.forEach(event => {
        eventTypes[event.eventType] = (eventTypes[event.eventType] || 0) + 1;
    });

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            totalEvents: events.length,
            eventTypes,
            recentEvents: events.slice(0, 10)
        })
    };
}

async function getUserStats(event) {
    const userId = 'user-123';

    // Get user's recipes count
    const recipeParams = {
        TableName: TABLE_NAME,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
            ':gsi1pk': 'RECIPE'
        }
    };

    const recipeResult = await dynamodb.query(recipeParams).promise();

    // Get user's favorites count
    const favoriteParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'FAVORITE#'
        }
    };

    const favoriteResult = await dynamodb.query(favoriteParams).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            recipesCount: recipeResult.Items?.length || 0,
            favoritesCount: favoriteResult.Items?.length || 0,
            joinDate: '2024-01-01',
            lastActivity: new Date().toISOString()
        })
    };
}
