const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/lifeplans')) {
            return await getLifePlans(event);
        } else if (httpMethod === 'POST' && path.includes('/lifeplans')) {
            return await createLifePlan(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/lifeplan/')) {
            return await getLifePlanById(event);
        } else if (httpMethod === 'PUT' && path.includes('/lifeplan/')) {
            return await updateLifePlan(requestBody, event);
        } else if (httpMethod === 'DELETE' && path.includes('/lifeplan/')) {
            return await deleteLifePlan(event);
        } else if (httpMethod === 'POST' && path.includes('/lifeplan/goal')) {
            return await addGoal(requestBody, event);
        } else if (httpMethod === 'PUT' && path.includes('/lifeplan/goal/')) {
            return await updateGoal(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getLifePlans(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LIFEPLAN#'
        }
    };
    
    const result = await dynamodb.query(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            lifePlans: result.Items || []
        })
    };
}

async function createLifePlan(body, event) {
    const userId = 'user-123';
    const planId = `lifeplan-${Date.now()}`;
    const { title, description, category, targetDate, goals } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`,
            GSI1PK: 'LIFEPLAN',
            GSI1SK: planId,
            id: planId,
            userId,
            title,
            description,
            category,
            targetDate,
            goals: goals || [],
            progress: 0,
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    };
    
    await dynamodb.put(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life plan created successfully',
            lifePlan: params.Item
        })
    };
}

async function getLifePlanById(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'lifeplan-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            lifePlan: result.Item || null
        })
    };
}

async function updateLifePlan(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'lifeplan-123';
    const { title, description, category, targetDate, progress, status } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        },
        UpdateExpression: 'SET title = :title, description = :description, category = :category, targetDate = :targetDate, progress = :progress, #status = :status, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#status': 'status'
        },
        ExpressionAttributeValues: {
            ':title': title,
            ':description': description,
            ':category': category,
            ':targetDate': targetDate,
            ':progress': progress,
            ':status': status,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life plan updated successfully',
            lifePlan: result.Attributes
        })
    };
}

async function deleteLifePlan(event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.id || 'lifeplan-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        }
    };
    
    await dynamodb.delete(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Life plan deleted successfully'
        })
    };
}

async function addGoal(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.planId || body.planId;
    const goalId = `goal-${Date.now()}`;
    const { title, description, targetDate, priority } = body;
    
    const goal = {
        id: goalId,
        title,
        description,
        targetDate,
        priority: priority || 'medium',
        completed: false,
        createdAt: new Date().toISOString()
    };
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        },
        UpdateExpression: 'SET goals = list_append(if_not_exists(goals, :empty_list), :goal), updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':goal': [goal],
            ':empty_list': [],
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Goal added successfully',
            goal,
            lifePlan: result.Attributes
        })
    };
}

async function updateGoal(body, event) {
    const userId = 'user-123';
    const planId = event.pathParameters?.planId || body.planId;
    const goalId = event.pathParameters?.goalId || body.goalId;
    const { completed } = body;
    
    // This is a simplified version - in a real implementation, you'd need to
    // update the specific goal in the goals array
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: `LIFEPLAN#${planId}`
        },
        UpdateExpression: 'SET updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Goal updated successfully',
            lifePlan: result.Attributes
        })
    };
}
