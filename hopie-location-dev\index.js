const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'POST' && path.includes('/location/update')) {
            return await updateLocation(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/location/current')) {
            return await getCurrentLocation(event);
        } else if (httpMethod === 'GET' && path.includes('/location/history')) {
            return await getLocationHistory(event);
        } else if (httpMethod === 'POST' && path.includes('/location/share')) {
            return await shareLocation(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/location/shared')) {
            return await getSharedLocations(event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function updateLocation(body, event) {
    const userId = 'user-123';
    const { latitude, longitude, accuracy, timestamp } = body;
    const locationId = `location-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `LOCATION#${locationId}`,
            id: locationId,
            userId,
            latitude,
            longitude,
            accuracy,
            timestamp: timestamp || new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    // Also update current location
    const currentParams = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'CURRENT_LOCATION'
        },
        UpdateExpression: 'SET latitude = :latitude, longitude = :longitude, accuracy = :accuracy, #timestamp = :timestamp, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#timestamp': 'timestamp'
        },
        ExpressionAttributeValues: {
            ':latitude': latitude,
            ':longitude': longitude,
            ':accuracy': accuracy,
            ':timestamp': timestamp || new Date().toISOString(),
            ':updatedAt': new Date().toISOString()
        }
    };

    await dynamodb.update(currentParams).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Location updated successfully',
            location: params.Item
        })
    };
}

async function getCurrentLocation(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'CURRENT_LOCATION'
        }
    };

    const result = await dynamodb.get(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            currentLocation: result.Item || null
        })
    };
}

async function getLocationHistory(event) {
    const userId = 'user-123';
    const limit = parseInt(event.queryStringParameters?.limit || '50');

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LOCATION#'
        },
        ScanIndexForward: false,
        Limit: limit
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            locationHistory: result.Items || []
        })
    };
}

async function shareLocation(body, event) {
    const userId = 'user-123';
    const { shareWithUserId, duration, message } = body;
    const shareId = `share-${Date.now()}`;

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + (duration || 1)); // Default 1 hour

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `USER#${userId}`,
            SK: `LOCATION_SHARE#${shareId}`,
            id: shareId,
            userId,
            shareWithUserId,
            message,
            expiresAt: expiresAt.toISOString(),
            active: true,
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Location sharing enabled successfully',
            share: params.Item
        })
    };
}

async function getSharedLocations(event) {
    const userId = 'user-123';

    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LOCATION_SHARE#'
        },
        FilterExpression: 'active = :active AND expiresAt > :now',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LOCATION_SHARE#',
            ':active': true,
            ':now': new Date().toISOString()
        }
    };

    const result = await dynamodb.query(params).promise();

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            sharedLocations: result.Items || []
        })
    };
}
