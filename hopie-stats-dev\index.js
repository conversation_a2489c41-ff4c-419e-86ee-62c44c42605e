const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};

    try {
        if (httpMethod === 'GET' && path.includes('/stats/user')) {
            return await getUserStats(event);
        } else if (httpMethod === 'GET' && path.includes('/stats/couple')) {
            return await getCoupleStats(event);
        } else if (httpMethod === 'GET' && path.includes('/stats/activities')) {
            return await getActivityStats(event);
        } else if (httpMethod === 'GET' && path.includes('/stats/progress')) {
            return await getProgressStats(event);
        } else if (httpMethod === 'POST' && path.includes('/stats/track')) {
            return await trackActivity(requestBody, event);
        }

        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserStats(event) {
    const userId = 'user-123';

    // Get various user statistics
    const promises = [
        // Get recipes count
        dynamodb.query({
            TableName: TABLE_NAME,
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :gsi1pk',
            ExpressionAttributeValues: { ':gsi1pk': 'RECIPE' }
        }).promise(),

        // Get favorites count
        dynamodb.query({
            TableName: TABLE_NAME,
            KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues: { ':pk': `USER#${userId}`, ':sk': 'FAVORITE#' }
        }).promise(),

        // Get trees count
        dynamodb.query({
            TableName: TABLE_NAME,
            KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues: { ':pk': `USER#${userId}`, ':sk': 'TREE#' }
        }).promise(),

        // Get life plans count
        dynamodb.query({
            TableName: TABLE_NAME,
            KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues: { ':pk': `USER#${userId}`, ':sk': 'LIFEPLAN#' }
        }).promise()
    ];

    const [recipesResult, favoritesResult, treesResult, lifePlansResult] = await Promise.all(promises);

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            userStats: {
                recipesCount: recipesResult.Items?.length || 0,
                favoritesCount: favoritesResult.Items?.length || 0,
                treesCount: treesResult.Items?.length || 0,
                lifePlansCount: lifePlansResult.Items?.length || 0,
                joinDate: '2024-01-01',
                lastActivity: new Date().toISOString()
            }
        })
    };
}

async function getCoupleStats(event) {
    const userId = 'user-123';

    // Get couple-related statistics
    const coupleParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'COUPLE#'
        }
    };

    const coupleResult = await dynamodb.query(coupleParams).promise();
    const activeCouples = (coupleResult.Items || []).filter(couple => couple.status === 'active');

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            coupleStats: {
                activeCouples: activeCouples.length,
                totalCouples: coupleResult.Items?.length || 0,
                relationshipDuration: activeCouples.length > 0 ?
                    Math.floor((new Date() - new Date(activeCouples[0].startDate)) / (1000 * 60 * 60 * 24)) : 0
            }
        })
    };
}

async function getActivityStats(event) {
    const userId = 'user-123';
    const timeframe = event.queryStringParameters?.timeframe || 'week'; // week, month, year

    // Calculate date range based on timeframe
    const now = new Date();
    const startDate = new Date();

    switch (timeframe) {
        case 'week':
            startDate.setDate(now.getDate() - 7);
            break;
        case 'month':
            startDate.setMonth(now.getMonth() - 1);
            break;
        case 'year':
            startDate.setFullYear(now.getFullYear() - 1);
            break;
    }

    // Get activities in the timeframe
    const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `ANALYTICS#${userId}`,
            ':sk': 'EVENT#'
        },
        FilterExpression: '#timestamp >= :startDate',
        ExpressionAttributeNames: {
            '#timestamp': 'timestamp'
        },
        ExpressionAttributeValues: {
            ':pk': `ANALYTICS#${userId}`,
            ':sk': 'EVENT#',
            ':startDate': startDate.toISOString()
        }
    };

    const result = await dynamodb.query(params).promise();
    const activities = result.Items || [];

    // Group activities by type
    const activityCounts = {};
    activities.forEach(activity => {
        activityCounts[activity.eventType] = (activityCounts[activity.eventType] || 0) + 1;
    });

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            activityStats: {
                timeframe,
                totalActivities: activities.length,
                activityCounts,
                dailyAverage: Math.round(activities.length / 7) // Simplified calculation
            }
        })
    };
}

async function getProgressStats(event) {
    const userId = 'user-123';

    // Get progress on life plans
    const lifePlansParams = {
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `USER#${userId}`,
            ':sk': 'LIFEPLAN#'
        }
    };

    const lifePlansResult = await dynamodb.query(lifePlansParams).promise();
    const lifePlans = lifePlansResult.Items || [];

    const totalProgress = lifePlans.reduce((sum, plan) => sum + (plan.progress || 0), 0);
    const averageProgress = lifePlans.length > 0 ? totalProgress / lifePlans.length : 0;

    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            progressStats: {
                totalLifePlans: lifePlans.length,
                averageProgress: Math.round(averageProgress),
                completedPlans: lifePlans.filter(plan => plan.progress >= 100).length,
                activePlans: lifePlans.filter(plan => plan.status === 'active').length
            }
        })
    };
}

async function trackActivity(body, event) {
    const userId = 'user-123';
    const { activityType, value, metadata } = body;
    const activityId = `activity-${Date.now()}`;

    const params = {
        TableName: TABLE_NAME,
        Item: {
            PK: `ANALYTICS#${userId}`,
            SK: `EVENT#${activityId}`,
            id: activityId,
            userId,
            eventType: activityType,
            eventData: { value, metadata },
            timestamp: new Date().toISOString(),
            createdAt: new Date().toISOString()
        }
    };

    await dynamodb.put(params).promise();

    return {
        statusCode: 201,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Activity tracked successfully',
            activity: params.Item
        })
    };
}
