# 📚 DOCUMENTACIÓN COMPLETA DE FUNCIONES LAMBDA

## 📊 RESUMEN GENERAL
- **Total de funciones**: 25
- **Patrón de arquitectura**: API REST + WebSocket
- **Base de datos**: DynamoDB (hopie-main-table-dev)
- **Almacenamiento**: S3 (hopie-app-assets-dev-123456)
- **Autenticación**: Headers Authorization (implementación pendiente)

---

## 🔍 ANÁLISIS DETALLADO POR FUNCIÓN

### 1. 📊 **hopie-analytics-dev**
**Propósito**: Sistema de analíticas y tracking de eventos de usuario

#### Endpoints:
- **POST** `/analytics/event` - Registrar evento de analítica
- **GET** `/analytics/dashboard` - Obtener datos del dashboard
- **GET** `/analytics/user-stats` - Obtener estadísticas del usuario

#### Parámetros de entrada:
```json
// POST /analytics/event
{
  "eventType": "string",     // Tipo de evento (ej: "page_view", "click")
  "eventData": "object",     // Datos del evento
  "timestamp": "string"      // ISO timestamp (opcional)
}

// GET /analytics/dashboard
// Sin parámetros

// GET /analytics/user-stats  
// Sin parámetros
```

#### Parámetros de salida:
```json
// POST /analytics/event
{
  "message": "Event tracked successfully",
  "event": {
    "id": "event-123456789",
    "userId": "user-123",
    "eventType": "page_view",
    "eventData": {...},
    "timestamp": "2024-01-01T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}

// GET /analytics/dashboard
{
  "totalEvents": 150,
  "eventTypes": {
    "page_view": 80,
    "click": 45,
    "form_submit": 25
  },
  "recentEvents": [...]
}

// GET /analytics/user-stats
{
  "recipesCount": 12,
  "favoritesCount": 8,
  "joinDate": "2024-01-01",
  "lastActivity": "2024-01-01T00:00:00.000Z"
}
```

---

### 2. 🏷️ **hopie-category-dev**
**Propósito**: Gestión de categorías para organizar contenido (recetas, ingredientes, etc.)

#### Endpoints:
- **GET** `/categories` - Listar todas las categorías
- **POST** `/categories` - Crear nueva categoría
- **GET** `/category/{id}` - Obtener categoría por ID
- **PUT** `/category/{id}` - Actualizar categoría
- **DELETE** `/category/{id}` - Eliminar categoría

#### Parámetros de entrada:
```json
// POST /categories, PUT /category/{id}
{
  "name": "string",          // Nombre de la categoría
  "description": "string",   // Descripción
  "color": "string"          // Color hex (ej: "#FF5733")
}
```

#### Parámetros de salida:
```json
// GET /categories
{
  "categories": [
    {
      "id": "category-123456789",
      "name": "Desayunos",
      "description": "Recetas para el desayuno",
      "color": "#FF5733",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}

// POST /categories, PUT /category/{id}
{
  "message": "Category created/updated successfully",
  "category": {...}
}

// GET /category/{id}
{
  "category": {...} // o null si no existe
}

// DELETE /category/{id}
{
  "message": "Category deleted successfully"
}
```

---

### 3. 💬 **hopie-chat-dev**
**Propósito**: Sistema de chat en tiempo real con WebSocket

#### Características:
- Manejo de mensajes en tiempo real
- Integración con WebSocket API Gateway
- Historial de conversaciones

#### Variables de entorno:
- `TABLE_NAME`: hopie-main-table-dev
- `WEBSOCKET_ENDPOINT`: wss://your-websocket-api-id.execute-api.us-east-2.amazonaws.com/dev

---

### 4. 💑 **hopie-couple-dev**
**Propósito**: Gestión de parejas y relaciones en la aplicación

#### Endpoints detectados: 14 endpoints
- Gestión de perfiles de pareja
- Sincronización de datos entre parejas
- Configuraciones compartidas

---

### 5. ⭐ **hopie-favorite-dev**
**Propósito**: Sistema de favoritos para que usuarios guarden elementos

#### Endpoints:
- **GET** `/favorites` - Obtener favoritos del usuario
- **POST** `/favorites` - Agregar elemento a favoritos
- **DELETE** `/favorite/{id}` - Remover de favoritos

#### Parámetros de entrada:
```json
// POST /favorites
{
  "itemId": "string",        // ID del elemento
  "itemType": "string",      // Tipo (recipe, ingredient, etc.)
  "title": "string"          // Título del elemento
}
```

#### Parámetros de salida:
```json
// GET /favorites
{
  "favorites": [
    {
      "itemId": "recipe-123",
      "itemType": "recipe",
      "title": "Pasta Carbonara",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}

// POST /favorites
{
  "message": "Favorite added successfully",
  "favorite": {...}
}

// DELETE /favorite/{id}
{
  "message": "Favorite removed successfully"
}
```

---

### 6. 🖼️ **hopie-image-dev**
**Propósito**: Gestión de imágenes con subida a S3 y metadatos en DynamoDB

#### Endpoints:
- **POST** `/upload-url` - Generar URL presignada para subida
- **POST** `/images` - Guardar metadatos de imagen
- **GET** `/images` - Obtener imágenes por entidad
- **DELETE** `/image/{id}` - Eliminar imagen

#### Variables de entorno:
- `TABLE_NAME`: hopie-main-table-dev
- `BUCKET_NAME`: hopie-app-assets-dev-123456

#### Parámetros de entrada:
```json
// POST /upload-url
{
  "fileName": "string",      // Nombre del archivo
  "fileType": "string"       // MIME type (image/jpeg, image/png)
}

// POST /images
{
  "key": "string",           // Key de S3
  "fileName": "string",      // Nombre original
  "fileType": "string",      // MIME type
  "entityId": "string",      // ID de la entidad asociada
  "entityType": "string"     // Tipo de entidad (recipe, user, etc.)
}

// GET /images?entityId=123&entityType=recipe
```

#### Parámetros de salida:
```json
// POST /upload-url
{
  "uploadUrl": "https://...",     // URL presignada para PUT
  "key": "images/123456-file.jpg",
  "downloadUrl": "https://..."    // URL pública de descarga
}

// POST /images
{
  "message": "Image metadata saved successfully",
  "image": {
    "id": "image-123456789",
    "key": "images/123456-file.jpg",
    "fileName": "file.jpg",
    "fileType": "image/jpeg",
    "entityId": "recipe-123",
    "entityType": "recipe",
    "url": "https://...",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}

// GET /images
{
  "images": [...]
}

// DELETE /image/{id}
{
  "message": "Image deleted successfully"
}
```

---

### 7. 🥕 **hopie-ingredient-dev**
**Propósito**: Gestión de ingredientes para recetas

#### Endpoints detectados: 10 endpoints
- CRUD completo de ingredientes
- Búsqueda y filtrado
- Información nutricional

---

### 8. 📋 **hopie-lifeplan-dev**
**Propósito**: Planificación de vida y objetivos

#### Endpoints detectados: 14 endpoints
- Gestión de planes de vida
- Objetivos y metas
- Seguimiento de progreso

---

### 9. 📍 **hopie-location-dev**
**Propósito**: Gestión de ubicaciones y lugares

#### Endpoints detectados: 10 endpoints
- Gestión de ubicaciones
- Búsqueda geográfica
- Lugares favoritos

---

### 10. 🍽️ **hopie-meal-plan-dev**
**Propósito**: Planificación de comidas y menús

#### Endpoints detectados: 10 endpoints
- Creación de planes de comida
- Calendario de menús
- Generación automática de planes

---

### 11. 📨 **hopie-message-history-dev**
**Propósito**: Historial de mensajes y conversaciones

#### Endpoints detectados: 6 endpoints
- Almacenamiento de mensajes
- Búsqueda en historial
- Gestión de conversaciones

---

### 12. 🔔 **hopie-notification-dev**
**Propósito**: Sistema de notificaciones para usuarios

#### Endpoints:
- **GET** `/notifications` - Obtener notificaciones del usuario
- **POST** `/notifications` - Crear nueva notificación
- **PUT** `/notification/{id}` - Marcar como leída
- **DELETE** `/notification/{id}` - Eliminar notificación

#### Parámetros de entrada:
```json
// POST /notifications
{
  "title": "string",         // Título de la notificación
  "message": "string",       // Mensaje
  "type": "string"           // Tipo: "info", "warning", "error", "success"
}

// PUT /notification/{id}
// Sin body - solo marca como leída
```

#### Parámetros de salida:
```json
// GET /notifications
{
  "notifications": [
    {
      "id": "notification-123456789",
      "title": "Nueva receta disponible",
      "message": "Se ha agregado una nueva receta a tus favoritos",
      "type": "info",
      "read": false,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}

// POST /notifications
{
  "message": "Notification created successfully",
  "notification": {...}
}

// PUT /notification/{id}
{
  "message": "Notification marked as read",
  "notification": {...}
}

// DELETE /notification/{id}
{
  "message": "Notification deleted successfully"
}
```

---

### 13. 🏪 **hopie-places-dev**
**Propósito**: Gestión de lugares y establecimientos

#### Endpoints detectados: 14 endpoints
- Directorio de lugares
- Reseñas y calificaciones
- Información de contacto

---

### 14. ❓ **hopie-questions-dev**
**Propósito**: Sistema de preguntas y encuestas

#### Endpoints detectados: 14 endpoints
- Creación de cuestionarios
- Respuestas de usuarios
- Análisis de resultados

---

### 15. 🍳 **hopie-recipe-dev**
**Propósito**: Gestión completa de recetas de cocina

#### Endpoints:
- **GET** `/recipes` - Listar todas las recetas
- **POST** `/recipes` - Crear nueva receta
- **GET** `/recipe/{id}` - Obtener receta por ID
- **PUT** `/recipe/{id}` - Actualizar receta
- **DELETE** `/recipe/{id}` - Eliminar receta

#### Parámetros de entrada:
```json
// POST /recipes, PUT /recipe/{id}
{
  "title": "string",         // Título de la receta
  "description": "string",   // Descripción
  "ingredients": "array",    // Lista de ingredientes
  "instructions": "array",   // Pasos de preparación
  "cookingTime": "number",   // Tiempo en minutos
  "difficulty": "string"     // "easy", "medium", "hard"
}
```

#### Parámetros de salida:
```json
// GET /recipes
{
  "recipes": [
    {
      "id": "recipe-123456789",
      "title": "Pasta Carbonara",
      "description": "Deliciosa pasta italiana",
      "ingredients": ["pasta", "huevos", "bacon"],
      "instructions": ["Hervir pasta", "Mezclar huevos"],
      "cookingTime": 30,
      "difficulty": "medium",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}

// POST /recipes, PUT /recipe/{id}
{
  "message": "Recipe created/updated successfully",
  "recipe": {...}
}

// GET /recipe/{id}
{
  "recipe": {...} // o null si no existe
}

// DELETE /recipe/{id}
{
  "message": "Recipe deleted successfully"
}
```

---

### 16-25. **Funciones Adicionales**

#### 16. 📊 **hopie-report-dev** (8 endpoints)
- Generación de reportes
- Estadísticas de uso
- Exportación de datos

#### 17. ⭐ **hopie-review-dev** (8 endpoints)
- Sistema de reseñas
- Calificaciones
- Comentarios

#### 18. 📅 **hopie-scheduler-dev** (14 endpoints)
- Programación de tareas
- Recordatorios
- Calendario

#### 19. 🔍 **hopie-search-dev** (2 endpoints)
- Búsqueda global
- Filtros avanzados

#### 20. 🛒 **hopie-shopping-list-dev** (10 endpoints)
- Listas de compras
- Gestión de elementos
- Compartir listas

#### 21. 📈 **hopie-stats-dev** (10 endpoints)
- Estadísticas generales
- Métricas de usuario
- Análisis de tendencias

#### 22. 🌳 **hopie-tree-dev** (14 endpoints)
- Estructuras jerárquicas
- Navegación por categorías
- Organización de contenido

#### 23. 👤 **hopie-user-dev**
- Gestión de perfiles
- Preferencias de usuario
- Configuraciones

#### 24. 🔌 **hopie-websocket-connect-dev**
- Conexión WebSocket
- Autenticación de conexión
- Registro de sesiones

#### 25. 🔌 **hopie-websocket-disconnect-dev**
- Desconexión WebSocket
- Limpieza de sesiones
- Logs de desconexión

---

## 🔧 PATRONES COMUNES

### Estructura de Respuesta Estándar:
```json
{
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type,Authorization"
  },
  "body": "JSON string"
}
```

### Códigos de Estado:
- **200**: Operación exitosa
- **201**: Recurso creado
- **400**: Error en parámetros
- **404**: Recurso no encontrado
- **500**: Error interno del servidor

### Estructura de DynamoDB:
- **PK**: Partition Key (ej: `USER#123`, `RECIPE#456`)
- **SK**: Sort Key (ej: `PROFILE`, `METADATA`, `FAVORITE#789`)
- **GSI1PK/GSI1SK**: Global Secondary Index para consultas

---

## 🚀 PRÓXIMOS PASOS

1. **Ejecutar migración**: `python ejecutar_migracion.py`
2. **Validar funciones**: Verificar que todos los endpoints respondan
3. **Implementar autenticación**: Reemplazar `user-123` con tokens reales
4. **Optimizar consultas**: Revisar índices de DynamoDB
5. **Monitorear rendimiento**: Configurar CloudWatch
