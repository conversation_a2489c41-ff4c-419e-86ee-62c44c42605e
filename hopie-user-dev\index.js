const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.TABLE_NAME;

exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const { httpMethod, path, body } = event;
    const requestBody = body ? JSON.parse(body) : {};
    
    try {
        if (httpMethod === 'GET' && path.includes('/profile')) {
            return await getUserProfile(event);
        } else if (httpMethod === 'PUT' && path.includes('/profile')) {
            return await updateUserProfile(requestBody, event);
        } else if (httpMethod === 'GET' && path.includes('/preferences')) {
            return await getUserPreferences(event);
        } else if (httpMethod === 'PUT' && path.includes('/preferences')) {
            return await updateUserPreferences(requestBody, event);
        }
        
        return {
            statusCode: 404,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ message: 'Endpoint not found' })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

async function getUserProfile(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            user: result.Item || { id: userId, name: 'Usuario Demo', email: '<EMAIL>' }
        })
    };
}

async function updateUserProfile(body, event) {
    const userId = 'user-123';
    const { name, email, phone, birthDate } = body;
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PROFILE'
        },
        UpdateExpression: 'SET #name = :name, email = :email, phone = :phone, birthDate = :birthDate, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
            '#name': 'name'
        },
        ExpressionAttributeValues: {
            ':name': name,
            ':email': email,
            ':phone': phone,
            ':birthDate': birthDate,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Profile updated successfully',
            user: result.Attributes
        })
    };
}

async function getUserPreferences(event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        }
    };
    
    const result = await dynamodb.get(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            preferences: result.Item || {
                notifications: true,
                theme: 'light',
                language: 'es'
            }
        })
    };
}

async function updateUserPreferences(body, event) {
    const userId = 'user-123';
    
    const params = {
        TableName: TABLE_NAME,
        Key: {
            PK: `USER#${userId}`,
            SK: 'PREFERENCES'
        },
        UpdateExpression: 'SET preferences = :preferences, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
            ':preferences': body,
            ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'ALL_NEW'
    };
    
    const result = await dynamodb.update(params).promise();
    
    return {
        statusCode: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
            message: 'Preferences updated successfully',
            preferences: result.Attributes
        })
    };
}
